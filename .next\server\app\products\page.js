/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/products/page";
exports.ids = ["app/products/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'products',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/page.tsx */ \"(rsc)/./src/app/products/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/products/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/products/page\",\n        pathname: \"/products\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwcm9kdWN0cyUyRnBhZ2UmcGFnZT0lMkZwcm9kdWN0cyUyRnBhZ2UmYXBwUGF0aHM9JTJGcHJvZHVjdHMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcHJvZHVjdHMlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsa0tBQTRIO0FBQ25KO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUFvSDtBQUM3SSxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbHhmYXN0ZW5lci13ZWJzaXRlLz84YzMwIl0sInNvdXJjZXNDb250ZW50IjpbIlwiVFVSQk9QQUNLIHsgdHJhbnNpdGlvbjogbmV4dC1zc3IgfVwiO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ3Byb2R1Y3RzJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcbHhmYXN0ZW5lclxcXFxzcmNcXFxcYXBwXFxcXHByb2R1Y3RzXFxcXHBhZ2UudHN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXEFkbWluXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGx4ZmFzdGVuZXJcXFxcc3JjXFxcXGFwcFxcXFxwcm9kdWN0c1xcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcbHhmYXN0ZW5lclxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcbHhmYXN0ZW5lclxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXEFkbWluXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGx4ZmFzdGVuZXJcXFxcc3JjXFxcXGFwcFxcXFxwcm9kdWN0c1xcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL3Byb2R1Y3RzL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcHJvZHVjdHMvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvcHJvZHVjdHNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Clayout%5CFooter.tsx&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Cproducts%5CProductCategories.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Clayout%5CFooter.tsx&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Cproducts%5CProductCategories.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/products/ProductCategories.tsx */ \"(ssr)/./src/components/products/ProductCategories.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0FkbWluJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q2x4ZmFzdGVuZXIlNUNzcmMlNUNjb21wb25lbnRzJTVDbGF5b3V0JTVDRm9vdGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0FkbWluJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q2x4ZmFzdGVuZXIlNUNzcmMlNUNjb21wb25lbnRzJTVDbGF5b3V0JTVDSGVhZGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0FkbWluJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q2x4ZmFzdGVuZXIlNUNzcmMlNUNjb21wb25lbnRzJTVDcHJvZHVjdHMlNUNQcm9kdWN0Q2F0ZWdvcmllcy50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUF5STtBQUN6SSxnTEFBbUk7QUFDbkksZ0xBQW1JO0FBQ25JIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbHhmYXN0ZW5lci13ZWJzaXRlLz9jMTNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcbHhmYXN0ZW5lclxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxGb290ZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxIZWFkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb2R1Y3RzXFxcXFByb2R1Y3RDYXRlZ29yaWVzLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Clayout%5CFooter.tsx&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccomponents%5Cproducts%5CProductCategories.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/LanguageContext.tsx */ \"(ssr)/./src/contexts/LanguageContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1pbnRlciUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIyUm9ib3RvJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyd2VpZ2h0JTIyJTNBJTVCJTIyMzAwJTIyJTJDJTIyNDAwJTIyJTJDJTIyNTAwJTIyJTJDJTIyNzAwJTIyJTVEJTJDJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTJDJTIydmFyaWFibGUlMjIlM0ElMjItLWZvbnQtcm9ib3RvJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIycm9ib3RvJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q3NyYyU1Q2NvbnRleHRzJTVDTGFuZ3VhZ2VDb250ZXh0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9seGZhc3RlbmVyLXdlYnNpdGUvPzFmNzEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxMYW5ndWFnZUNvbnRleHQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Footer = ()=>{\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const productCategories = [\n        {\n            name: t(\"products.bolts\"),\n            href: \"/products/bolts\"\n        },\n        {\n            name: t(\"products.nuts\"),\n            href: \"/products/nuts\"\n        },\n        {\n            name: t(\"products.screws\"),\n            href: \"/products/screws\"\n        },\n        {\n            name: t(\"products.studs\"),\n            href: \"/products/studs\"\n        },\n        {\n            name: t(\"products.washers\"),\n            href: \"/products/washers\"\n        }\n    ];\n    const quickLinks = [\n        {\n            name: t(\"nav.home\"),\n            href: \"/\"\n        },\n        {\n            name: t(\"nav.about\"),\n            href: \"/about\"\n        },\n        {\n            name: t(\"nav.products\"),\n            href: \"/products\"\n        },\n        {\n            name: t(\"nav.technical\"),\n            href: \"/technical\"\n        },\n        {\n            name: t(\"nav.contact\"),\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold\",\n                                                children: \"联勋\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold\",\n                                                children: t(\"about.companyName\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm leading-relaxed mb-6\",\n                                    children: t(\"footer.description\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"footer.productCategories\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: productCategories.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors text-sm\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"footer.quickLinks\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: quickLinks.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors text-sm\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"footer.getInTouch\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-500 mt-1 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm\",\n                                                    children: t(\"footer.address\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300 text-sm\",\n                                                    children: \"86-15373477521\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: t(\"footer.copyright\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                        children: \"隐私政策\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                        children: \"使用条款\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(ssr)/./src/components/layout/LanguageSwitcher.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProductsOpen, setIsProductsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const navigation = [\n        {\n            name: t(\"nav.home\"),\n            href: \"/\"\n        },\n        {\n            name: t(\"nav.about\"),\n            href: \"/about\"\n        },\n        {\n            name: t(\"nav.products\"),\n            href: \"/products\",\n            submenu: [\n                {\n                    name: t(\"products.bolts\"),\n                    href: \"/products/bolts\"\n                },\n                {\n                    name: t(\"products.nuts\"),\n                    href: \"/products/nuts\"\n                },\n                {\n                    name: t(\"products.screws\"),\n                    href: \"/products/screws\"\n                },\n                {\n                    name: t(\"products.studs\"),\n                    href: \"/products/studs\"\n                },\n                {\n                    name: t(\"products.washers\"),\n                    href: \"/products/washers\"\n                }\n            ]\n        },\n        {\n            name: t(\"nav.technical\"),\n            href: \"/technical\"\n        },\n        {\n            name: t(\"nav.contact\"),\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 lg:h-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        src: \"/images/logo.png\",\n                                        alt: \"联勋金属 Lianxun Metal\",\n                                        width: 200,\n                                        height: 120,\n                                        className: \"h-full w-auto object-contain\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"flex items-center text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors h-10\",\n                                                children: [\n                                                    item.name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"ml-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: item.href,\n                                                            className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors font-medium\",\n                                                            children: t(\"products.viewAll\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t border-gray-100 my-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        item.submenu.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: subItem.href,\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors\",\n                                                                children: subItem.name\n                                                            }, subItem.name, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 76,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"flex items-center text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors h-10\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    className: \"lg:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100 transition-colors\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 29\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 57\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 bg-white border-t\",\n                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsProductsOpen(!isProductsOpen),\n                                            className: \"flex items-center justify-between w-full text-left text-gray-700 hover:text-primary-600 hover:bg-gray-50 px-3 py-2 text-base font-medium transition-colors\",\n                                            children: [\n                                                item.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: `h-4 w-4 transition-transform ${isProductsOpen ? \"rotate-180\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        isProductsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-4 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: \"block text-gray-600 hover:text-primary-600 hover:bg-gray-50 px-3 py-2 text-sm transition-colors font-medium\",\n                                                    onClick: ()=>setIsMenuOpen(false),\n                                                    children: t(\"products.viewAll\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-200 my-2 ml-3 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                item.submenu.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: subItem.href,\n                                                        className: \"block text-gray-600 hover:text-primary-600 hover:bg-gray-50 px-3 py-2 text-sm transition-colors\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        children: subItem.name\n                                                    }, subItem.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 29\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 21\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"block text-gray-700 hover:text-primary-600 hover:bg-gray-50 px-3 py-2 text-base font-medium transition-colors\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, item.name, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/LanguageSwitcher.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/LanguageSwitcher.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst LanguageSwitcher = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { language, setLanguage } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const currentLanguage = _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.languages.find((lang)=>lang.code === language) || _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.languages[0];\n    const handleLanguageChange = (langCode)=>{\n        setLanguage(langCode);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-primary-600 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hidden sm:inline\",\n                        children: currentLanguage.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sm:hidden\",\n                        children: currentLanguage.flag\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: `h-3 w-3 transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1 max-h-64 overflow-y-auto\",\n                    children: _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleLanguageChange(lang.code),\n                            className: `w-full text-left flex items-center space-x-3 px-4 py-2 text-sm hover:bg-gray-50 transition-colors ${currentLanguage.code === lang.code ? \"bg-primary-50 text-primary-600\" : \"text-gray-700\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg\",\n                                    children: lang.flag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: lang.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, lang.code, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/products/ProductCategories.tsx":
/*!*******************************************************!*\
  !*** ./src/components/products/ProductCategories.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst categories = [\n    {\n        id: \"all\",\n        name: \"全部产品\",\n        count: 120,\n        href: \"/products\"\n    },\n    {\n        id: \"bolts\",\n        name: \"螺栓\",\n        count: 45,\n        href: \"/products/bolts\",\n        subcategories: [\n            {\n                name: \"六角螺栓\",\n                href: \"/products/bolts/hex\"\n            },\n            {\n                name: \"法兰螺栓\",\n                href: \"/products/bolts/flange\"\n            },\n            {\n                name: \"马车螺栓\",\n                href: \"/products/bolts/carriage\"\n            },\n            {\n                name: \"吊环螺栓\",\n                href: \"/products/bolts/eye\"\n            },\n            {\n                name: \"U型螺栓\",\n                href: \"/products/bolts/u-bolt\"\n            }\n        ]\n    },\n    {\n        id: \"nuts\",\n        name: \"螺母\",\n        count: 32,\n        href: \"/products/nuts\",\n        subcategories: [\n            {\n                name: \"六角螺母\",\n                href: \"/products/nuts/hex\"\n            },\n            {\n                name: \"法兰螺母\",\n                href: \"/products/nuts/flange\"\n            },\n            {\n                name: \"锁紧螺母\",\n                href: \"/products/nuts/lock\"\n            },\n            {\n                name: \"蝶形螺母\",\n                href: \"/products/nuts/wing\"\n            }\n        ]\n    },\n    {\n        id: \"screws\",\n        name: \"螺钉\",\n        count: 28,\n        href: \"/products/screws\",\n        subcategories: [\n            {\n                name: \"自攻螺钉\",\n                href: \"/products/screws/self-tapping\"\n            },\n            {\n                name: \"机器螺钉\",\n                href: \"/products/screws/machine\"\n            },\n            {\n                name: \"木螺钉\",\n                href: \"/products/screws/wood\"\n            }\n        ]\n    },\n    {\n        id: \"studs\",\n        name: \"螺柱\",\n        count: 15,\n        href: \"/products/studs\",\n        subcategories: [\n            {\n                name: \"双头螺柱\",\n                href: \"/products/studs/double-end\"\n            },\n            {\n                name: \"焊接螺柱\",\n                href: \"/products/studs/welding\"\n            }\n        ]\n    },\n    {\n        id: \"washers\",\n        name: \"垫圈\",\n        count: 20,\n        href: \"/products/washers\",\n        subcategories: [\n            {\n                name: \"平垫圈\",\n                href: \"/products/washers/flat\"\n            },\n            {\n                name: \"弹簧垫圈\",\n                href: \"/products/washers/spring\"\n            },\n            {\n                name: \"锁紧垫圈\",\n                href: \"/products/washers/lock\"\n            }\n        ]\n    }\n];\nconst ProductCategories = ()=>{\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-12 bg-white border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-4 mb-8\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveCategory(category.id),\n                            className: `px-6 py-3 rounded-lg font-medium transition-all duration-200 ${activeCategory === category.id ? \"bg-primary-600 text-white shadow-lg\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"}`,\n                            children: [\n                                category.name,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm opacity-75\",\n                                    children: [\n                                        \"(\",\n                                        category.count,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                activeCategory !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-xl p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: [\n                                categories.find((c)=>c.id === activeCategory)?.name,\n                                \" 子分类\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                            children: categories.find((c)=>c.id === activeCategory)?.subcategories?.map((sub)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: sub.href,\n                                    className: \"bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 text-center group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-700 group-hover:text-primary-600 font-medium\",\n                                        children: sub.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, sub.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductCategories.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductCategories);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/products/ProductCategories.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _public_locales_zh_common_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/locales/zh/common.json */ \"(ssr)/./public/locales/zh/common.json\");\n/* harmony import */ var _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../public/locales/en/common.json */ \"(ssr)/./public/locales/en/common.json\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage,languages auto */ \n\n// 直接导入翻译文件\n\n\n// 翻译数据存储 - 直接使用导入的数据\nconst translations = {\n    zh: _public_locales_zh_common_json__WEBPACK_IMPORTED_MODULE_2__,\n    en: _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__\n};\n// 获取嵌套对象的值\nconst getNestedValue = (obj, path)=>{\n    return path.split(\".\").reduce((current, key)=>{\n        return current && current[key] !== undefined ? current[key] : undefined;\n    }, obj);\n};\n// 创建上下文\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"zh\");\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用 useLayoutEffect 在渲染前同步读取 localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (false) {}\n    }, []);\n    // 服务端渲染时立即标记为已初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            setIsInitialized(true);\n        }\n    }, []);\n    // 设置语言并保存到 localStorage\n    const setLanguage = (lang)=>{\n        setLanguageState(lang);\n        if (false) {}\n    };\n    // 翻译函数\n    const t = (key)=>{\n        const translation = getNestedValue(translations[language], key);\n        if (!translation) {\n            console.warn(`Translation missing for key: ${key} in language: ${language}`);\n            return key // 返回键名作为后备\n            ;\n        }\n        return translation;\n    };\n    const value = {\n        language,\n        setLanguage,\n        t\n    };\n    // 在初始化完成前不渲染内容，避免闪烁\n    if (!isInitialized) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n// 使用语言上下文的Hook\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n// 导出语言列表供其他组件使用\nconst languages = [\n    {\n        code: \"zh\",\n        name: \"简体中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9c3cc20aed3a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbHhmYXN0ZW5lci13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz82Mjc3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOWMzY2MyMGFlZDNhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Roboto\",\"arguments\":[{\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-roboto\"}],\"variableName\":\"roboto\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Roboto\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-roboto\\\"}],\\\"variableName\\\":\\\"roboto\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./src/contexts/LanguageContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"联勋紧固件有限公司 - 专业紧固件制造商\",\n    description: \"联勋紧固件有限公司是一家专业的紧固件制造商，专注于设计和生产高品质紧固件。我们提供螺栓、螺母、螺钉、螺柱、垫圈等全系列紧固件产品。\",\n    keywords: \"紧固件,螺栓,螺母,螺钉,螺柱,垫圈,联勋紧固件\",\n    authors: [\n        {\n            name: \"联勋紧固件有限公司\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"联勋紧固件有限公司 - 专业紧固件制造商\",\n        description: \"联勋紧固件有限公司是一家专业的紧固件制造商，专注于设计和生产高品质紧固件。\",\n        type: \"website\",\n        locale: \"zh_CN\",\n        siteName: \"联勋紧固件有限公司\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_products_ProductGrid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/products/ProductGrid */ \"(rsc)/./src/components/products/ProductGrid.tsx\");\n/* harmony import */ var _components_products_ProductCategories__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/products/ProductCategories */ \"(rsc)/./src/components/products/ProductCategories.tsx\");\n\n\n\n\n\nfunction ProductsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-r from-primary-600 to-primary-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-white mb-6\",\n                            children: \"产品中心\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-primary-100 max-w-3xl mx-auto\",\n                            children: \"我们提供全系列高品质紧固件产品，满足各种工业应用需求\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_ProductCategories__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_ProductGrid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/products/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\components\layout\Footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\components\layout\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/products/ProductCategories.tsx":
/*!*******************************************************!*\
  !*** ./src/components/products/ProductCategories.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\components\products\ProductCategories.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/products/ProductGrid.tsx":
/*!*************************************************!*\
  !*** ./src/components/products/ProductGrid.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n\n\n\nconst products = [\n    {\n        id: 1,\n        name: \"结构螺栓\",\n        description: \"高强度结构连接螺栓，适用于钢结构建筑\",\n        specifications: \"M12-M30, 8.8-10.9级\",\n        image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n        category: \"bolts\",\n        href: \"/products/structure-bolts\"\n    },\n    {\n        id: 2,\n        name: \"剪切焊接螺柱\",\n        description: \"专业焊接连接螺柱，用于钢板连接\",\n        specifications: \"M6-M20, 碳钢/不锈钢\",\n        image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n        category: \"studs\",\n        href: \"/products/shear-welding-studs\"\n    },\n    {\n        id: 3,\n        name: \"六角螺栓\",\n        description: \"标准六角头螺栓，通用性强\",\n        specifications: \"M6-M36, 4.8-12.9级\",\n        image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n        category: \"bolts\",\n        href: \"/products/hex-bolts\"\n    },\n    {\n        id: 4,\n        name: \"六角法兰螺栓\",\n        description: \"带法兰盘六角螺栓，分布载荷均匀\",\n        specifications: \"M6-M20, 8.8-10.9级\",\n        image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n        category: \"bolts\",\n        href: \"/products/hex-flange-bolts\"\n    },\n    {\n        id: 5,\n        name: \"马车螺栓\",\n        description: \"方颈马车螺栓，防转动设计\",\n        specifications: \"M6-M20, 4.8-8.8级\",\n        image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n        category: \"bolts\",\n        href: \"/products/carriage-bolts\"\n    },\n    {\n        id: 6,\n        name: \"吊环螺栓\",\n        description: \"起重用吊环螺栓，安全可靠\",\n        specifications: \"M8-M24, 载重等级A-C\",\n        image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n        category: \"bolts\",\n        href: \"/products/eye-bolts\"\n    },\n    {\n        id: 7,\n        name: \"地脚锚栓\",\n        description: \"建筑地基锚固螺栓，承载力强\",\n        specifications: \"M16-M48, Q235-Q345\",\n        image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n        category: \"bolts\",\n        href: \"/products/foundation-anchor-bolts\"\n    },\n    {\n        id: 8,\n        name: \"U型螺栓\",\n        description: \"管道固定U型螺栓，抱箍连接\",\n        specifications: \"M8-M30, 镀锌/不锈钢\",\n        image: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n        category: \"bolts\",\n        href: \"/products/u-bolts\"\n    }\n];\nconst ProductGrid = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 lg:py-24 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: product.href,\n                            className: \"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square overflow-hidden relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-cover bg-center bg-no-repeat group-hover:scale-110 transition-transform duration-500\",\n                                            style: {\n                                                backgroundImage: `url('${product.image}')`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"h-8 w-8 mx-auto mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"查看详情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors\",\n                                            children: product.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 bg-gray-50 px-3 py-1 rounded-full inline-block\",\n                                            children: product.specifications\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 px-8 py-3 rounded-lg font-medium transition-colors duration-200\",\n                        children: \"加载更多产品\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductGrid.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductGrid);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/products/ProductGrid.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   languages: () => (/* binding */ e2),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#useLanguage`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#languages`);


/***/ }),

/***/ "(ssr)/./public/locales/en/common.json":
/*!***************************************!*\
  !*** ./public/locales/en/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"nav":{"home":"Home","about":"About Us","products":"Products","technical":"Technical","contact":"Contact Us"},"hero":{"title1":"Professional Fastener Manufacturer","title2":"Quality Assurance, Precision Manufacturing","title3":"Global Service, Trustworthy","subtitle1":"Lianxun Fastener Co., Ltd","subtitle2":"10 Years Professional Experience","subtitle3":"Serving Global Customers","description1":"Specializing in the design, production and sales of high-quality fasteners, providing professional fastening system solutions for global customers","description2":"With advanced cold forging, hot forging workshops, heat treatment equipment, strict quality control system ensures product quality","description3":"Create benefits for customers, opportunities for employees, and value for society","cta1":"Learn More","cta2":"View Products","cta3":"Contact Us"},"about":{"title":"About Us","companyName":"Lianxun Fastener Co., Ltd","description":"Lianxun Fastener Co., Ltd is a professional Chinese fastener manufacturer specializing in the design and production of top-notch fasteners. Located in Yongnian District, Handan City - the \'Capital of Fasteners in China\', it covers a business area of 7,000 square meters including cold forging workshop, hot forging workshop, heat treatment workshop, warehouse and laboratory.","description2":"Currently, the company has more than 80 sets of production and testing equipment and more than 100 employees. We are committed to providing professional and efficient fastening system solutions for global customers. With corporate values of integrity, unity and innovation, we create benefits for customers, opportunities for employees, and value for society.","readMore":"Read More","stats":{"experience":"Years Experience","employees":"Professional Staff","equipment":"Production Equipment","facility":"Square Meters Facility"},"certification":"Quality Certification"},"products":{"title":"Products List","subtitle":"We provide a full range of high-quality fastener products to meet various industrial application needs","description":"We provide a full range of high-quality fastener products to meet various industrial application needs","viewMore":"View More Products","viewAll":"View All Products","bolts":"Bolts","nuts":"Nuts","screws":"Screws","studs":"Studs","washers":"Washers","categories":{"bolts":{"title":"Bolt Products","subtitle":"High-strength bolt products suitable for various engineering connections","hexBolt":{"name":"Hex Bolts","description":"Standard hex head bolts with strong versatility, suitable for various mechanical connections"},"flangeBolt":{"name":"Flange Bolts","description":"Bolts with flange plates to increase contact area and distribute loads"},"uBolt":{"name":"U Bolt","description":"Specialized U-bolts for pipe fixing, clamp connection, easy installation","shortDesc":"Specialized U-bolts for pipe fixing, clamp connection, easy installation"}},"nuts":{"title":"Nut Products","subtitle":"High-quality nut products, perfectly matched with bolts","hexNut":{"name":"Hex Nuts","description":"Standard hex nuts, used with bolts for reliable connections"},"flangeNut":{"name":"Flange Nuts","description":"Nuts with flange plates to increase contact area and distribute loads"}},"screws":{"title":"Screw Products","subtitle":"Precision screw products to meet various fastening needs","selfTapping":{"name":"Self-Tapping Screws","description":"Self-tapping screws that require no pre-drilling and directly tap threads for connection"},"machine":{"name":"Machine Screws","description":"Machine screws with precision threads, suitable for mechanical equipment"}},"studs":{"title":"Stud Products","subtitle":"Professional stud products providing reliable double-end connections","doubleEnd":{"name":"Double-End Studs","description":"Studs threaded at both ends for through connections"},"welding":{"name":"Welding Studs","description":"Studs specifically designed for welding connections, providing strong and reliable welds"}},"washers":{"title":"Washer Products","subtitle":"Quality washer products providing effective sealing and protection","flat":{"name":"Flat Washers","description":"Standard flat washers to increase contact area and prevent loosening"},"spring":{"name":"Spring Washers","description":"Elastic washers providing preload force to prevent bolt loosening"}}}},"specifications":{"spec":"Specification","strength":"Strength Grade","material":"Material","surface":"Surface Treatment","shape":"Shape","thickness":"Thickness","headType":"Head Type","weldingMethod":"Welding Method","length":"Length"},"applications":{"mechanical":"Mechanical Equipment","construction":"Construction Structure","automotive":"Automotive Industry","electrical":"Electrical Equipment","precision":"Precision Equipment","high-strength":"High-Strength Connection","pipe-fixing":"Pipe Fixing","cable-tray":"Cable Tray","equipment":"Equipment Installation","structure":"Structure Connection","thin-plate":"Thin Plate Connection","plastic":"Plastic Fixing","electronics":"Electronic Equipment","furniture":"Furniture Manufacturing","precision-machinery":"Precision Machinery","instruments":"Instruments","automation":"Automation Equipment","flange":"Flange Connection","pipeline":"Pipeline Connection","steel-structure":"Steel Structure","shipbuilding":"Shipbuilding","bridge":"Bridge Engineering","curtain-wall":"Curtain Wall","bolt-connection":"Bolt Connection","equipment-installation":"Equipment Installation","structural-fixing":"Structural Fixing"},"surfaceTreatments":{"zinc":"Zinc Plating","blackening":"Blackening","phosphating":"Phosphating","dacromet":"Dacromet","nickel":"Nickel Plating","stainless":"Stainless Steel Natural","passivation":"Passivation"},"hexBolt":{"spec":"M6-M36","strength":"4.8, 6.8, 8.8, 10.9, 12.9","material":"Q235, 35K, 45K, 40Cr","surface":"Zinc Plating, Blackening, Phosphating"},"flangeBolt":{"spec":"M6-M20","strength":"8.8, 10.9","material":"35K, 45K, 40Cr","surface":"Zinc Plating, Dacromet"},"uBolt":{"spec":"M8-M30","material":"Q235, 304 Stainless Steel","surface":"Zinc Plating, Stainless Steel Natural","shape":"Standard U-type, Extended U-type"},"product":{"structureBolts":{"name":"Structure Bolts","desc":"High-strength structural connection bolts for steel construction","description":"Structure bolts are high-strength fasteners specifically designed for steel structure connections, featuring excellent mechanical properties and corrosion resistance. Widely used in building steel structures, bridge engineering, tower structures and other important engineering projects.","specs":{"grade":"Strength Grade","size":"Size Range","length":"Length Range","material":"Material","surface":"Surface Treatment"},"apps":{"steel":"Steel Structure Buildings","bridge":"Bridge Engineering","tower":"Tower Structures","industrial":"Industrial Buildings"},"features":{"highStrength":"High strength design with strong load capacity","corrosionResistant":"Excellent corrosion resistance performance","precisionThreads":"Precision threads for reliable connections","qualityTested":"Strict quality testing, meets national standards"}},"shearWeldingStuds":{"name":"Shear Welding Studs","desc":"Professional welding studs providing reliable shear connections","description":"Shear welding studs are connectors fixed to steel structures through arc welding, mainly used to transfer shear forces in steel-concrete composite structures. Features fast welding, reliable connections, and convenient construction.","specs":{"diameter":"Diameter","length":"Length","material":"Material","weldTime":"Welding Time"},"apps":{"composite":"Steel-Concrete Composite Structures","deck":"Steel Bar Truss Floor Deck","precast":"Precast Component Connections"},"features":{"fastWelding":"Fast welding with high efficiency","strongShear":"Strong shear load capacity","precisePosition":"Precise positioning with stable quality"}},"hexBolts":{"name":"Hex Bolts","desc":"Standard hex head bolts widely used in mechanical connections","description":"Hex bolts are one of the most commonly used fasteners, featuring high standardization, strong versatility, and easy installation. Suitable for connections and fixings in various mechanical equipment, building structures, automotive manufacturing and other fields.","specs":{"grade":"Strength Grade","size":"Size Range","length":"Length Range","material":"Material"},"apps":{"machinery":"Mechanical Equipment","automotive":"Automotive Manufacturing","construction":"Construction Engineering","equipment":"Equipment Installation"},"features":{"versatile":"Strong versatility with wide applications","standardized":"Standardized production with good interchangeability","reliable":"Reliable connections with stable performance"}},"hexFlangeBolts":{"name":"Hex Flange Bolts","desc":"Hex bolts with flange for increased contact area"},"carriageBolts":{"name":"Carriage Bolts","desc":"Square neck bolts preventing rotation, suitable for wood connections"},"eyeBolts":{"name":"Eye Bolts","desc":"Bolts with ring head for lifting and traction applications"},"foundationAnchorBolts":{"name":"Foundation Anchor Bolts","desc":"Anchor bolts for equipment foundation fixing"},"uBolts":{"name":"U-Bolts","desc":"U-shaped bolts for pipe and round object fixing"}},"productDetail":{"specifications":"Technical Specifications","applications":"Applications","features":"Product Features","relatedProducts":"Related Products","getQuote":"Get Quote","downloadSpec":"Download Datasheet","backToProducts":"Back to Products","gallery":"Product Gallery","overview":"Product Overview"},"culture":{"title":"Corporate Culture","subtitle":"Our core values guide our development direction and shape our corporate spirit","mission":"Corporate Mission","vision":"Corporate Vision","values":"Corporate Values","missionDesc":"Provide professional and efficient fastening system solutions for customers worldwide","visionDesc":"Create benefits for customers, opportunities for employees, and value for society","valuesDesc":"Honesty, Trustworthiness, Unity, Innovation","quality":{"title":"Quality Commitment","description":"We strictly implement the ISO 9001:2015 quality management system standards. From raw material procurement to finished product delivery, every link undergoes strict quality control. Our laboratory is equipped with advanced testing equipment to ensure that every product meets international standards and customer requirements.","certification":"ISO 9001:2015 Quality Management System Certification","materials":"Strict raw material inspection standards","process":"Perfect production process control","testing":"Comprehensive finished product testing system"}},"technical":{"title":"Technical Support","subtitle":"Professional technical documentation and solutions","description":"We provide comprehensive technical support and professional guidance to help you choose the most suitable fastener solutions","featuredArticle":"Featured Article","technicalDocs":"Technical Documentation","readFull":"Read Full Article","readMore":"Read More","readTime":"read","downloadTitle":"Technical Documentation Download","downloadDesc":"We provide complete product technical documentation and specification downloads, including CAD drawings, technical parameter tables, installation guides, etc.","downloadBtn":"Download Technical Documentation Package","categories":{"guide":"Technical Guide","standard":"Standards & Specifications","data":"Technical Data","calculation":"Calculation Methods","analysis":"Case Analysis"},"articles":{"coarseVsFine":{"title":"Comparison of Coarse and Fine Threads","excerpt":"Are coarse threads better or fine threads better? This is a question we often hear about inserts and external threaded fasteners. This article will analyze the characteristics, application scenarios and selection criteria of both thread types.","date":"January 6, 2025"},"surfaceTreatment":{"title":"European Surface Treatment Codes and Standards","excerpt":"Detailed introduction to the code system and technical specifications for fastener surface treatment in European standards, including standard requirements for various surface treatment processes such as galvanizing, blackening, and phosphating.","date":"January 6, 2025"},"hardnessConversion":{"title":"Hardness Conversion Chart","excerpt":"Approximate conversion values for steel Rockwell C hardness, including comparison relationships between HRC, HV, HB and other hardness standards, as well as the application range of various hardness testing methods.","date":"January 6, 2025"},"materialSelection":{"title":"Fastener Material Selection Guide","excerpt":"How to select appropriate fastener materials based on application environment, including the characteristics and applicable scenarios of carbon steel, alloy steel, stainless steel and other materials.","date":"January 5, 2025"},"boltPreload":{"title":"Bolt Preload Calculation Methods","excerpt":"Detailed introduction to bolt preload calculation methods, influencing factors and control standards to ensure connection reliability and safety.","date":"January 4, 2025"},"failureAnalysis":{"title":"Fastener Failure Analysis Cases","excerpt":"Analysis of fastener failure causes through actual cases, including preventive measures for common problems such as fatigue fracture, corrosion failure, and overload fracture.","date":"January 3, 2025"}}},"contact":{"title":"Contact Us","subtitle":"We look forward to working with you to provide professional fastener solutions","info":{"title":"Contact Information","description":"We are always ready to provide you with professional services and support. Whether you have any questions about our products or need customized fastener solutions, please feel free to contact us.","address":"Company Address","phone":"Phone","mobile":"Mobile/WeChat","email":"Email","salesEmail":"Sales Email","workTime":"Working Hours","location":"Company Location","mapLoading":"Loading map...","businessHours":"Business Hours","monday":"Monday - Friday","saturday":"Saturday","sunday":"Sunday","closed":"Closed"},"form":{"title":"Send Message","name":"Name","email":"Email","phone":"Phone","company":"Company Name","subject":"Subject","message":"Message","required":"*","placeholders":{"name":"Please enter your name","email":"Please enter your email","phone":"Please enter your phone","company":"Please enter your company name","subject":"Please select a subject","message":"Please describe your requirements or questions in detail..."},"subjects":{"select":"Please select a subject","product":"Product Inquiry","technical":"Technical Support","quotation":"Quotation Request","partnership":"Partnership","other":"Other"},"submit":"Send Message","submitting":"Sending...","success":{"title":"Message sent successfully!","message":"Thank you for your inquiry, we will reply within 24 hours."}}},"footer":{"address":"Yongnian District, Handan City, Hebei Province, China","description":"Professional industrial fastener manufacturer specializing in the design and production of structural fasteners of all kinds. Over 10 years\' experience in providing our customers with high quality materials at competitive prices and best service.","productCategories":"Product Categories","quickLinks":"Quick Links","getInTouch":"Get In Touch","copyright":"© 2024 Lianxun Fastener Co., Ltd. All Rights Reserved."}}');

/***/ }),

/***/ "(ssr)/./public/locales/zh/common.json":
/*!***************************************!*\
  !*** ./public/locales/zh/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"nav":{"home":"首页","about":"关于我们","products":"产品中心","technical":"技术支持","contact":"联系我们"},"hero":{"title1":"专业紧固件制造商","title2":"品质保证 精工制造","title3":"全球服务 值得信赖","subtitle1":"联勋紧固件有限公司","subtitle2":"10年专业经验","subtitle3":"服务全球客户","description1":"专注于高品质紧固件的设计、生产和销售，为全球客户提供专业的紧固系统解决方案","description2":"拥有先进的冷锻、热锻车间，热处理设备，严格的质量控制体系确保产品品质","description3":"为客户创造效益，为员工创造机会，为社会创造价值","cta1":"了解更多","cta2":"查看产品","cta3":"联系我们"},"about":{"title":"关于我们","companyName":"联勋紧固件有限公司","description":"联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。公司位于\\"中国紧固件之都\\"——河北省邯郸市永年区，占地面积7000平方米，包括冷锻车间、热锻车间、热处理车间、仓库和实验室。","description2":"目前，公司拥有80多套生产和检测设备，员工100多人。我们致力于为全球客户提供专业高效的紧固系统解决方案，以诚信、团结、创新的企业价值观，为客户创造效益，为员工创造机会，为社会创造价值。","readMore":"了解更多","stats":{"experience":"年专业经验","employees":"专业员工","equipment":"生产设备","facility":"平方米厂房"},"certification":"质量认证"},"products":{"title":"产品列表","subtitle":"我们提供全系列高品质紧固件产品，满足各种工业应用需求","description":"我们提供全系列高品质紧固件产品，满足各种工业应用需求","viewMore":"查看更多产品","viewAll":"查看全部产品","bolts":"螺栓","nuts":"螺母","screws":"螺钉","studs":"螺柱","washers":"垫圈","categories":{"bolts":{"title":"螺栓产品","subtitle":"高强度螺栓产品，适用于各种工程连接","hexBolt":{"name":"六角螺栓","description":"标准六角头螺栓，通用性强，适用于各种机械连接"},"flangeBolt":{"name":"法兰螺栓","description":"带法兰盘的螺栓，增大接触面积，分散载荷"},"uBolt":{"name":"U型螺栓","description":"管道固定专用U型螺栓，抱箍连接，安装方便","shortDesc":"管道固定专用U型螺栓，抱箍连接，安装方便"}},"nuts":{"title":"螺母产品","subtitle":"高品质螺母产品，与螺栓完美配套","hexNut":{"name":"六角螺母","description":"标准六角螺母，与螺栓配套使用，连接可靠"},"flangeNut":{"name":"法兰螺母","description":"带法兰盘的螺母，增大接触面积，分散载荷"}},"screws":{"title":"螺钉产品","subtitle":"精密螺钉产品，满足各种固定需求","selfTapping":{"name":"自攻螺钉","description":"自攻螺钉，无需预钻孔，直接攻丝连接"},"machine":{"name":"机器螺钉","description":"机器螺钉，精密螺纹，适用于机械设备"}},"studs":{"title":"螺柱产品","subtitle":"专业螺柱产品，提供可靠的双端连接","doubleEnd":{"name":"双头螺柱","description":"两端均为螺纹的螺柱，用于穿透连接"},"welding":{"name":"焊接螺柱","description":"专用于焊接连接的螺柱，焊接牢固可靠"}},"washers":{"title":"垫圈产品","subtitle":"优质垫圈产品，提供有效的密封和保护","flat":{"name":"平垫圈","description":"标准平垫圈，增大接触面积，防止松动"},"spring":{"name":"弹簧垫圈","description":"弹性垫圈，提供预紧力，防止螺栓松动"}}}},"specifications":{"spec":"规格","strength":"强度等级","material":"材质","surface":"表面处理","shape":"形状","thickness":"厚度","headType":"头型","weldingMethod":"焊接方式","length":"长度"},"applications":{"mechanical":"机械设备","construction":"建筑结构","automotive":"汽车工业","electrical":"电力设备","precision":"精密设备","high-strength":"高强度连接","pipe-fixing":"管道固定","cable-tray":"电缆桥架","equipment":"设备安装","structure":"结构连接","thin-plate":"薄板连接","plastic":"塑料固定","electronics":"电子设备","furniture":"家具制造","precision-machinery":"精密机械","instruments":"仪器仪表","automation":"自动化设备","flange":"法兰连接","pipeline":"管道连接","steel-structure":"钢结构","shipbuilding":"船舶制造","bridge":"桥梁工程","curtain-wall":"建筑幕墙","bolt-connection":"螺栓连接","equipment-installation":"设备安装","structural-fixing":"结构固定"},"surfaceTreatments":{"zinc":"镀锌","blackening":"发黑","phosphating":"磷化","dacromet":"达克罗","nickel":"镀镍","stainless":"不锈钢本色","passivation":"钝化"},"hexBolt":{"spec":"M6-M36","strength":"4.8, 6.8, 8.8, 10.9, 12.9","material":"Q235, 35K, 45K, 40Cr","surface":"镀锌、发黑、磷化"},"flangeBolt":{"spec":"M6-M20","strength":"8.8, 10.9","material":"35K, 45K, 40Cr","surface":"镀锌、达克罗"},"uBolt":{"spec":"M8-M30","material":"Q235, 304不锈钢","surface":"镀锌、不锈钢本色","shape":"标准U型、加长U型"},"product":{"structureBolts":{"name":"结构螺栓","desc":"高强度结构连接螺栓，适用于钢结构建筑","description":"结构螺栓是专为钢结构连接设计的高强度紧固件，具有优异的机械性能和耐腐蚀性能。广泛应用于建筑钢结构、桥梁工程、塔架结构等重要工程项目中。","specs":{"grade":"强度等级","size":"规格尺寸","length":"长度范围","material":"材质","surface":"表面处理"},"apps":{"steel":"钢结构建筑","bridge":"桥梁工程","tower":"塔架结构","industrial":"工业厂房"},"features":{"highStrength":"高强度设计，承载能力强","corrosionResistant":"优异的防腐蚀性能","precisionThreads":"精密螺纹，连接可靠","qualityTested":"严格质量检测，符合国标"}},"shearWeldingStuds":{"name":"剪切焊接螺柱","desc":"专业焊接螺柱，提供可靠的剪切连接","description":"剪切焊接螺柱是一种通过电弧焊接固定在钢结构上的连接件，主要用于钢-混凝土组合结构中传递剪力。具有焊接快速、连接可靠、施工方便等特点。","specs":{"diameter":"直径","length":"长度","material":"材质","weldTime":"焊接时间"},"apps":{"composite":"钢混组合结构","deck":"钢筋桁架楼承板","precast":"预制构件连接"},"features":{"fastWelding":"快速焊接，效率高","strongShear":"剪切承载力强","precisePosition":"定位精确，质量稳定"}},"hexBolts":{"name":"六角螺栓","desc":"标准六角头螺栓，广泛应用于机械连接","description":"六角螺栓是最常用的紧固件之一，具有标准化程度高、通用性强、安装方便等特点。适用于各种机械设备、建筑结构、汽车制造等领域的连接固定。","specs":{"grade":"强度等级","size":"规格尺寸","length":"长度范围","material":"材质"},"apps":{"machinery":"机械设备","automotive":"汽车制造","construction":"建筑工程","equipment":"设备安装"},"features":{"versatile":"通用性强，应用广泛","standardized":"标准化生产，互换性好","reliable":"连接可靠，性能稳定"}},"hexFlangeBolts":{"name":"六角法兰螺栓","desc":"带法兰盘的六角螺栓，增大接触面积"},"carriageBolts":{"name":"马车螺栓","desc":"方颈螺栓，防止转动，适用于木材连接"},"eyeBolts":{"name":"吊环螺栓","desc":"带环形头部的螺栓，用于起重和牵引"},"foundationAnchorBolts":{"name":"地脚锚栓","desc":"用于设备固定的地脚螺栓"},"uBolts":{"name":"U型螺栓","desc":"U形螺栓，用于管道和圆形物体固定"}},"productDetail":{"specifications":"技术规格","applications":"应用领域","features":"产品特点","relatedProducts":"相关产品","getQuote":"获取报价","downloadSpec":"下载规格书","backToProducts":"返回产品列表","gallery":"产品图册","overview":"产品概述"},"culture":{"title":"企业文化","subtitle":"我们的核心价值观指引着我们的发展方向，塑造着我们的企业精神","mission":"企业使命","vision":"企业愿景","values":"企业价值观","missionDesc":"为全球客户提供专业高效的紧固系统解决方案","visionDesc":"为客户创造效益，为员工创造机会，为社会创造价值","valuesDesc":"诚信、守信、团结、创新","quality":{"title":"质量承诺","description":"我们严格按照ISO 9001:2015质量管理体系标准执行，从原材料采购到成品出厂，每一个环节都经过严格的质量控制。我们的实验室配备了先进的检测设备，确保每一件产品都符合国际标准和客户要求。","certification":"ISO 9001:2015 质量管理体系认证","materials":"严格的原材料检验标准","process":"完善的生产过程控制","testing":"全面的成品检测体系"}},"technical":{"title":"技术支持","subtitle":"专业的技术文档和解决方案","description":"我们提供全面的技术支持和专业指导，帮助您选择最适合的紧固件解决方案","featuredArticle":"精选文章","technicalDocs":"技术文档","readFull":"阅读全文","readMore":"阅读更多","readTime":"阅读","downloadTitle":"技术资料下载","downloadDesc":"我们提供完整的产品技术资料和规格书下载，包括CAD图纸、技术参数表、安装指南等。","downloadBtn":"下载技术资料包","categories":{"guide":"技术指南","standard":"标准规范","data":"技术资料","calculation":"计算方法","analysis":"案例分析"},"articles":{"coarseVsFine":{"title":"粗牙螺纹与细牙螺纹的比较","excerpt":"粗牙螺纹好还是细牙螺纹好？这是我们公司经常听到的关于嵌件和外螺纹紧固件的问题。本文将详细分析两种螺纹的特点、应用场景和选择标准。","date":"2025年1月6日"},"surfaceTreatment":{"title":"欧洲表面处理代码和规范","excerpt":"详细介绍欧洲标准中关于紧固件表面处理的代码体系和技术规范，包括镀锌、发黑、磷化等各种表面处理工艺的标准要求。","date":"2025年1月6日"},"hardnessConversion":{"title":"硬度换算对照表","excerpt":"钢材洛氏C硬度的近似换算值，包括HRC、HV、HB等不同硬度标准的对照关系，以及各种硬度测试方法的应用范围。","date":"2025年1月6日"},"materialSelection":{"title":"紧固件材料选择指南","excerpt":"如何根据应用环境选择合适的紧固件材料，包括碳钢、合金钢、不锈钢等材料的特性和适用场景。","date":"2025年1月5日"},"boltPreload":{"title":"螺栓预紧力计算方法","excerpt":"详细介绍螺栓预紧力的计算方法、影响因素和控制标准，确保连接的可靠性和安全性。","date":"2025年1月4日"},"failureAnalysis":{"title":"紧固件失效分析案例","excerpt":"通过实际案例分析紧固件失效的原因，包括疲劳断裂、腐蚀失效、过载断裂等常见问题的预防措施。","date":"2025年1月3日"}}},"contact":{"title":"联系我们","subtitle":"我们期待与您合作，为您提供专业的紧固件解决方案","info":{"title":"联系信息","description":"我们随时准备为您提供专业的服务和支持。无论您有任何关于产品的问题，或者需要定制化的紧固件解决方案，请随时与我们联系。","address":"公司地址","phone":"联系电话","mobile":"手机/微信","email":"邮箱地址","salesEmail":"销售邮箱","workTime":"工作时间","location":"公司位置","mapLoading":"地图加载中...","businessHours":"营业时间","monday":"周一 - 周五","saturday":"周六","sunday":"周日","closed":"休息"},"form":{"title":"发送消息","name":"姓名","email":"邮箱","phone":"电话","company":"公司名称","subject":"咨询主题","message":"详细信息","required":"*","placeholders":{"name":"请输入您的姓名","email":"请输入您的邮箱","phone":"请输入您的电话","company":"请输入您的公司名称","subject":"请选择咨询主题","message":"请详细描述您的需求或问题..."},"subjects":{"select":"请选择咨询主题","product":"产品咨询","technical":"技术支持","quotation":"报价询问","partnership":"合作洽谈","other":"其他"},"submit":"发送消息","submitting":"发送中...","success":{"title":"消息发送成功！","message":"感谢您的咨询，我们会在24小时内回复您。"}}},"footer":{"address":"中国河北省邯郸市永年区刘营镇五里村工业区","description":"专业的工业紧固件制造商，专注于各类结构紧固件的设计与生产。拥有超过10年的经验，为客户提供高品质材料、具有竞争力的价格和最佳服务。","productCategories":"产品分类","quickLinks":"快速链接","getInTouch":"联系我们","copyright":"© 2024 联勋紧固件有限公司. 保留所有权利."}}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();