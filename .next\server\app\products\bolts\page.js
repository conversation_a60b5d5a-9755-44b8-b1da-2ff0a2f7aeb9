/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/products/bolts/page";
exports.ids = ["app/products/bolts/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fbolts%2Fpage&page=%2Fproducts%2Fbolts%2Fpage&appPaths=%2Fproducts%2Fbolts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fbolts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fbolts%2Fpage&page=%2Fproducts%2Fbolts%2Fpage&appPaths=%2Fproducts%2Fbolts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fbolts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'products',\n        {\n        children: [\n        'bolts',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/bolts/page.tsx */ \"(rsc)/./src/app/products/bolts/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/products/bolts/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/products/bolts/page\",\n        pathname: \"/products/bolts\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fbolts%2Fpage&page=%2Fproducts%2Fbolts%2Fpage&appPaths=%2Fproducts%2Fbolts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fbolts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q2FwcC1yb3V0ZXIuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNBZG1pbiU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNseGZhc3RlbmVyJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDZXJyb3ItYm91bmRhcnkuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNBZG1pbiU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNseGZhc3RlbmVyJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0FkbWluJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q2x4ZmFzdGVuZXIlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNub3QtZm91bmQtYm91bmRhcnkuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNBZG1pbiU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNseGZhc3RlbmVyJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0FkbWluJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q2x4ZmFzdGVuZXIlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTJKO0FBQzNKLDBPQUErSjtBQUMvSix3T0FBOEo7QUFDOUosa1BBQW1LO0FBQ25LLHNRQUE2SztBQUM3SyIsInNvdXJjZXMiOlsid2VicGFjazovL2x4ZmFzdGVuZXItd2Vic2l0ZS8/ZWU2NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFkbWluXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGx4ZmFzdGVuZXJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxhcHAtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFkbWluXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGx4ZmFzdGVuZXJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcbHhmYXN0ZW5lclxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHN0YXRpYy1nZW5lcmF0aW9uLXNlYXJjaHBhcmFtcy1iYWlsb3V0LXByb3ZpZGVyLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/LanguageContext.tsx */ \"(ssr)/./src/contexts/LanguageContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1pbnRlciUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIyUm9ib3RvJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyd2VpZ2h0JTIyJTNBJTVCJTIyMzAwJTIyJTJDJTIyNDAwJTIyJTJDJTIyNTAwJTIyJTJDJTIyNzAwJTIyJTVEJTJDJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTJDJTIydmFyaWFibGUlMjIlM0ElMjItLWZvbnQtcm9ib3RvJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIycm9ib3RvJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q3NyYyU1Q2NvbnRleHRzJTVDTGFuZ3VhZ2VDb250ZXh0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9seGZhc3RlbmVyLXdlYnNpdGUvPzFmNzEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxMYW5ndWFnZUNvbnRleHQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cproducts%5Cbolts%5Cpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cproducts%5Cbolts%5Cpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/bolts/page.tsx */ \"(ssr)/./src/app/products/bolts/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q3NyYyU1Q2FwcCU1Q3Byb2R1Y3RzJTVDYm9sdHMlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9seGZhc3RlbmVyLXdlYnNpdGUvPzE3ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXHNyY1xcXFxhcHBcXFxccHJvZHVjdHNcXFxcYm9sdHNcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cproducts%5Cbolts%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/products/bolts/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/products/bolts/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BoltsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_products_ProductDetail__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/products/ProductDetail */ \"(ssr)/./src/components/products/ProductDetail.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction BoltsPage() {\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const boltProducts = [\n        {\n            id: \"hex-bolt\",\n            slug: \"hex-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.hexBolt.name\",\n            descriptionKey: \"products.categories.bolts.hexBolt.description\",\n            shortDescKey: \"products.categories.bolts.hexBolt.description\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    valueKey: \"hexBolt.spec\"\n                },\n                {\n                    key: \"specifications.strength\",\n                    valueKey: \"hexBolt.strength\"\n                },\n                {\n                    key: \"specifications.material\",\n                    valueKey: \"hexBolt.material\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    valueKey: \"hexBolt.surface\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"applications.mechanical\"\n                },\n                {\n                    key: \"applications.construction\"\n                },\n                {\n                    key: \"applications.automotive\"\n                },\n                {\n                    key: \"applications.electrical\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"Hex Bolt\"\n                },\n                {\n                    url: \"https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n                    alt: \"Hex Bolt Detail\"\n                }\n            ],\n            features: []\n        },\n        {\n            id: \"flange-bolt\",\n            slug: \"flange-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.flangeBolt.name\",\n            descriptionKey: \"products.categories.bolts.flangeBolt.description\",\n            shortDescKey: \"products.categories.bolts.flangeBolt.description\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    valueKey: \"flangeBolt.spec\"\n                },\n                {\n                    key: \"specifications.strength\",\n                    valueKey: \"flangeBolt.strength\"\n                },\n                {\n                    key: \"specifications.material\",\n                    valueKey: \"flangeBolt.material\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    valueKey: \"flangeBolt.surface\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"applications.automotive\"\n                },\n                {\n                    key: \"applications.mechanical\"\n                },\n                {\n                    key: \"applications.precision\"\n                },\n                {\n                    key: \"applications.high-strength\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"Flange Bolt\"\n                }\n            ],\n            features: []\n        },\n        {\n            id: \"u-bolt\",\n            slug: \"u-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.uBolt.name\",\n            descriptionKey: \"products.categories.bolts.uBolt.description\",\n            shortDescKey: \"products.categories.bolts.uBolt.shortDesc\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    valueKey: \"uBolt.spec\"\n                },\n                {\n                    key: \"specifications.material\",\n                    valueKey: \"uBolt.material\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    valueKey: \"uBolt.surface\"\n                },\n                {\n                    key: \"specifications.shape\",\n                    valueKey: \"uBolt.shape\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"applications.pipe-fixing\"\n                },\n                {\n                    key: \"applications.cable-tray\"\n                },\n                {\n                    key: \"applications.equipment\"\n                },\n                {\n                    key: \"applications.structure\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"U Bolt\"\n                }\n            ],\n            features: []\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-r from-primary-600 to-primary-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-white mb-6\",\n                            children: t(\"products.categories.bolts.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-primary-100 max-w-3xl mx-auto\",\n                            children: t(\"products.categories.bolts.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 lg:py-24 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-16\",\n                        children: boltProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_ProductDetail__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                product: product\n                            }, product.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/products/bolts/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,MapPin,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Footer = ()=>{\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const productCategories = [\n        {\n            name: t(\"products.bolts\"),\n            href: \"/products/bolts\"\n        },\n        {\n            name: t(\"products.nuts\"),\n            href: \"/products/nuts\"\n        },\n        {\n            name: t(\"products.screws\"),\n            href: \"/products/screws\"\n        },\n        {\n            name: t(\"products.studs\"),\n            href: \"/products/studs\"\n        },\n        {\n            name: t(\"products.washers\"),\n            href: \"/products/washers\"\n        }\n    ];\n    const quickLinks = [\n        {\n            name: t(\"nav.home\"),\n            href: \"/\"\n        },\n        {\n            name: t(\"nav.about\"),\n            href: \"/about\"\n        },\n        {\n            name: t(\"nav.products\"),\n            href: \"/products\"\n        },\n        {\n            name: t(\"nav.technical\"),\n            href: \"/technical\"\n        },\n        {\n            name: t(\"nav.contact\"),\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold\",\n                                                children: \"联勋\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold\",\n                                                children: t(\"about.companyName\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm leading-relaxed mb-6\",\n                                    children: t(\"footer.description\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"footer.productCategories\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: productCategories.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors text-sm\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"footer.quickLinks\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: quickLinks.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors text-sm\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"footer.getInTouch\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-500 mt-1 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm\",\n                                                    children: t(\"footer.address\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_MapPin_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300 text-sm\",\n                                                    children: \"86-15373477521\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: t(\"footer.copyright\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                        children: \"隐私政策\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                        children: \"使用条款\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFNEI7QUFDeUQ7QUFDN0I7QUFFeEQsTUFBTU8sU0FBUztJQUNiLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdGLHNFQUFXQTtJQUV6QixNQUFNRyxvQkFBb0I7UUFDeEI7WUFBRUMsTUFBTUYsRUFBRTtZQUFtQkcsTUFBTTtRQUFrQjtRQUNyRDtZQUFFRCxNQUFNRixFQUFFO1lBQWtCRyxNQUFNO1FBQWlCO1FBQ25EO1lBQUVELE1BQU1GLEVBQUU7WUFBb0JHLE1BQU07UUFBbUI7UUFDdkQ7WUFBRUQsTUFBTUYsRUFBRTtZQUFtQkcsTUFBTTtRQUFrQjtRQUNyRDtZQUFFRCxNQUFNRixFQUFFO1lBQXFCRyxNQUFNO1FBQW9CO0tBQzFEO0lBRUQsTUFBTUMsYUFBYTtRQUNqQjtZQUFFRixNQUFNRixFQUFFO1lBQWFHLE1BQU07UUFBSTtRQUNqQztZQUFFRCxNQUFNRixFQUFFO1lBQWNHLE1BQU07UUFBUztRQUN2QztZQUFFRCxNQUFNRixFQUFFO1lBQWlCRyxNQUFNO1FBQVk7UUFDN0M7WUFBRUQsTUFBTUYsRUFBRTtZQUFrQkcsTUFBTTtRQUFhO1FBQy9DO1lBQUVELE1BQU1GLEVBQUU7WUFBZ0JHLE1BQU07UUFBVztLQUM1QztJQUVELHFCQUNFLDhEQUFDRTtRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNiLDRFQUFDRTtnREFBS0YsV0FBVTswREFBdUI7Ozs7Ozs7Ozs7O3NEQUV6Qyw4REFBQ0M7c0RBQ0MsNEVBQUNFO2dEQUFHSCxXQUFVOzBEQUFxQk4sRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR3pDLDhEQUFDVTtvQ0FBRUosV0FBVTs4Q0FDVk4sRUFBRTs7Ozs7OzhDQUVMLDhEQUFDTztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNLOzRDQUFFUixNQUFLOzRDQUFJRyxXQUFVO3NEQUNwQiw0RUFBQ2IsdUhBQVFBO2dEQUFDYSxXQUFVOzs7Ozs7Ozs7OztzREFFdEIsOERBQUNLOzRDQUFFUixNQUFLOzRDQUFJRyxXQUFVO3NEQUNwQiw0RUFBQ1osdUhBQVFBO2dEQUFDWSxXQUFVOzs7Ozs7Ozs7OztzREFFdEIsOERBQUNLOzRDQUFFUixNQUFLOzRDQUFJRyxXQUFVO3NEQUNwQiw0RUFBQ1gsdUhBQWFBO2dEQUFDVyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNL0IsOERBQUNDOzs4Q0FDQyw4REFBQ0U7b0NBQUdILFdBQVU7OENBQThCTixFQUFFOzs7Ozs7OENBQzlDLDhEQUFDWTtvQ0FBR04sV0FBVTs4Q0FDWEwsa0JBQWtCWSxHQUFHLENBQUMsQ0FBQ0MscUJBQ3RCLDhEQUFDQztzREFDQyw0RUFBQ3ZCLGtEQUFJQTtnREFDSFcsTUFBTVcsS0FBS1gsSUFBSTtnREFDZkcsV0FBVTswREFFVFEsS0FBS1osSUFBSTs7Ozs7OzJDQUxMWSxLQUFLWixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7O3NDQWF4Qiw4REFBQ0s7OzhDQUNDLDhEQUFDRTtvQ0FBR0gsV0FBVTs4Q0FBOEJOLEVBQUU7Ozs7Ozs4Q0FDOUMsOERBQUNZO29DQUFHTixXQUFVOzhDQUNYRixXQUFXUyxHQUFHLENBQUMsQ0FBQ0MscUJBQ2YsOERBQUNDO3NEQUNDLDRFQUFDdkIsa0RBQUlBO2dEQUNIVyxNQUFNVyxLQUFLWCxJQUFJO2dEQUNmRyxXQUFVOzBEQUVUUSxLQUFLWixJQUFJOzs7Ozs7MkNBTExZLEtBQUtaLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBYXhCLDhEQUFDSzs7OENBQ0MsOERBQUNFO29DQUFHSCxXQUFVOzhDQUE4Qk4sRUFBRTs7Ozs7OzhDQUM5Qyw4REFBQ087b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNULHVIQUFNQTtvREFBQ1MsV0FBVTs7Ozs7OzhEQUNsQiw4REFBQ0k7b0RBQUVKLFdBQVU7OERBQ1ZOLEVBQUU7Ozs7Ozs7Ozs7OztzREFHUCw4REFBQ087NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDVix1SEFBSUE7b0RBQUNVLFdBQVU7Ozs7Ozs4REFDaEIsOERBQUNLO29EQUFFUixNQUFLO29EQUE2QkcsV0FBVTs4REFBMkQ7Ozs7Ozs7Ozs7OztzREFJNUcsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ1YsdUhBQUlBO29EQUFDVSxXQUFVOzs7Ozs7OERBQ2hCLDhEQUFDSztvREFBRVIsTUFBSztvREFBOEJHLFdBQVU7OERBQTJEOzs7Ozs7Ozs7Ozs7c0RBSTdHLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNYLHVIQUFhQTtvREFBQ1csV0FBVTs7Ozs7OzhEQUN6Qiw4REFBQ0U7b0RBQUtGLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT2hELDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDSTtnQ0FBRUosV0FBVTswQ0FDVk4sRUFBRTs7Ozs7OzBDQUVMLDhEQUFDTztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNkLGtEQUFJQTt3Q0FBQ1csTUFBSzt3Q0FBV0csV0FBVTtrREFBMkQ7Ozs7OztrREFHM0YsOERBQUNkLGtEQUFJQTt3Q0FBQ1csTUFBSzt3Q0FBU0csV0FBVTtrREFBMkQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTdkc7QUFFQSxpRUFBZVAsTUFBTUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2x4ZmFzdGVuZXItd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9Gb290ZXIudHN4PzI2MzgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IEZhY2Vib29rLCBMaW5rZWRpbiwgTWVzc2FnZUNpcmNsZSwgUGhvbmUsIE1haWwsIE1hcFBpbiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IHVzZUxhbmd1YWdlIH0gZnJvbSAnQC9jb250ZXh0cy9MYW5ndWFnZUNvbnRleHQnXG5cbmNvbnN0IEZvb3RlciA9ICgpID0+IHtcbiAgY29uc3QgeyB0IH0gPSB1c2VMYW5ndWFnZSgpXG5cbiAgY29uc3QgcHJvZHVjdENhdGVnb3JpZXMgPSBbXG4gICAgeyBuYW1lOiB0KCdwcm9kdWN0cy5ib2x0cycpLCBocmVmOiAnL3Byb2R1Y3RzL2JvbHRzJyB9LFxuICAgIHsgbmFtZTogdCgncHJvZHVjdHMubnV0cycpLCBocmVmOiAnL3Byb2R1Y3RzL251dHMnIH0sXG4gICAgeyBuYW1lOiB0KCdwcm9kdWN0cy5zY3Jld3MnKSwgaHJlZjogJy9wcm9kdWN0cy9zY3Jld3MnIH0sXG4gICAgeyBuYW1lOiB0KCdwcm9kdWN0cy5zdHVkcycpLCBocmVmOiAnL3Byb2R1Y3RzL3N0dWRzJyB9LFxuICAgIHsgbmFtZTogdCgncHJvZHVjdHMud2FzaGVycycpLCBocmVmOiAnL3Byb2R1Y3RzL3dhc2hlcnMnIH0sXG4gIF1cblxuICBjb25zdCBxdWlja0xpbmtzID0gW1xuICAgIHsgbmFtZTogdCgnbmF2LmhvbWUnKSwgaHJlZjogJy8nIH0sXG4gICAgeyBuYW1lOiB0KCduYXYuYWJvdXQnKSwgaHJlZjogJy9hYm91dCcgfSxcbiAgICB7IG5hbWU6IHQoJ25hdi5wcm9kdWN0cycpLCBocmVmOiAnL3Byb2R1Y3RzJyB9LFxuICAgIHsgbmFtZTogdCgnbmF2LnRlY2huaWNhbCcpLCBocmVmOiAnL3RlY2huaWNhbCcgfSxcbiAgICB7IG5hbWU6IHQoJ25hdi5jb250YWN0JyksIGhyZWY6ICcvY29udGFjdCcgfSxcbiAgXVxuXG4gIHJldHVybiAoXG4gICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCB0ZXh0LXdoaXRlXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LTEyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtOFwiPlxuICAgICAgICAgIHsvKiBDb21wYW55IEluZm8gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctcHJpbWFyeS02MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtci0zXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGRcIj7ogZTli4s8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZFwiPnt0KCdhYm91dC5jb21wYW55TmFtZScpfTwvaDM+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtc20gbGVhZGluZy1yZWxheGVkIG1iLTZcIj5cbiAgICAgICAgICAgICAge3QoJ2Zvb3Rlci5kZXNjcmlwdGlvbicpfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgIDxGYWNlYm9vayBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgIDxMaW5rZWRpbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgIDxNZXNzYWdlQ2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBQcm9kdWN0IENhdGVnb3JpZXMgKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPnt0KCdmb290ZXIucHJvZHVjdENhdGVnb3JpZXMnKX08L2gzPlxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICB7cHJvZHVjdENhdGVnb3JpZXMubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBRdWljayBMaW5rcyAqL31cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+e3QoJ2Zvb3Rlci5xdWlja0xpbmtzJyl9PC9oMz5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge3F1aWNrTGlua3MubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBDb250YWN0IEluZm8gKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPnt0KCdmb290ZXIuZ2V0SW5Ub3VjaCcpfTwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPE1hcFBpbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtcHJpbWFyeS01MDAgbXQtMSBmbGV4LXNocmluay0wXCIgLz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIHt0KCdmb290ZXIuYWRkcmVzcycpfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPE1haWwgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXByaW1hcnktNTAwIGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCJtYWlsdG86aW5mb0BseGZhc3RlbmVyLmNvbVwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICBpbmZvQGx4ZmFzdGVuZXIuY29tXG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8TWFpbCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtcHJpbWFyeS01MDAgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cIm1haWx0bzpzYWxlc0BseGZhc3RlbmVyLmNvbVwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICBzYWxlc0BseGZhc3RlbmVyLmNvbVxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPE1lc3NhZ2VDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXByaW1hcnktNTAwIGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbVwiPjg2LTE1MzczNDc3NTIxPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQm90dG9tIEJhciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS04MDAgbXQtOCBwdC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICB7dCgnZm9vdGVyLmNvcHlyaWdodCcpfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNiBtdC00IG1kOm10LTBcIj5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wcml2YWN5XCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICDpmpDnp4HmlL/nrZZcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3Rlcm1zXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICDkvb/nlKjmnaHmrL5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9mb290ZXI+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgRm9vdGVyXG4iXSwibmFtZXMiOlsiTGluayIsIkZhY2Vib29rIiwiTGlua2VkaW4iLCJNZXNzYWdlQ2lyY2xlIiwiTWFpbCIsIk1hcFBpbiIsInVzZUxhbmd1YWdlIiwiRm9vdGVyIiwidCIsInByb2R1Y3RDYXRlZ29yaWVzIiwibmFtZSIsImhyZWYiLCJxdWlja0xpbmtzIiwiZm9vdGVyIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3BhbiIsImgzIiwicCIsImEiLCJ1bCIsIm1hcCIsIml0ZW0iLCJsaSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(ssr)/./src/components/layout/LanguageSwitcher.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProductsOpen, setIsProductsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const navigation = [\n        {\n            name: t(\"nav.home\"),\n            href: \"/\"\n        },\n        {\n            name: t(\"nav.about\"),\n            href: \"/about\"\n        },\n        {\n            name: t(\"nav.products\"),\n            href: \"/products\",\n            submenu: [\n                {\n                    name: t(\"products.bolts\"),\n                    href: \"/products/bolts\"\n                },\n                {\n                    name: t(\"products.nuts\"),\n                    href: \"/products/nuts\"\n                },\n                {\n                    name: t(\"products.screws\"),\n                    href: \"/products/screws\"\n                },\n                {\n                    name: t(\"products.studs\"),\n                    href: \"/products/studs\"\n                },\n                {\n                    name: t(\"products.washers\"),\n                    href: \"/products/washers\"\n                }\n            ]\n        },\n        {\n            name: t(\"nav.technical\"),\n            href: \"/technical\"\n        },\n        {\n            name: t(\"nav.contact\"),\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 lg:h-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        src: \"/images/logo.png\",\n                                        alt: \"联勋金属 Lianxun Metal\",\n                                        width: 200,\n                                        height: 120,\n                                        className: \"h-full w-auto object-contain\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"flex items-center text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors h-10\",\n                                                children: [\n                                                    item.name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"ml-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: item.href,\n                                                            className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors font-medium\",\n                                                            children: t(\"products.viewAll\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t border-gray-100 my-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        item.submenu.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: subItem.href,\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors\",\n                                                                children: subItem.name\n                                                            }, subItem.name, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 76,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"flex items-center text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors h-10\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    className: \"lg:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100 transition-colors\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 29\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 57\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 bg-white border-t\",\n                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsProductsOpen(!isProductsOpen),\n                                            className: \"flex items-center justify-between w-full text-left text-gray-700 hover:text-primary-600 hover:bg-gray-50 px-3 py-2 text-base font-medium transition-colors\",\n                                            children: [\n                                                item.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: `h-4 w-4 transition-transform ${isProductsOpen ? \"rotate-180\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        isProductsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-4 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: \"block text-gray-600 hover:text-primary-600 hover:bg-gray-50 px-3 py-2 text-sm transition-colors font-medium\",\n                                                    onClick: ()=>setIsMenuOpen(false),\n                                                    children: t(\"products.viewAll\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-200 my-2 ml-3 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                item.submenu.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: subItem.href,\n                                                        className: \"block text-gray-600 hover:text-primary-600 hover:bg-gray-50 px-3 py-2 text-sm transition-colors\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        children: subItem.name\n                                                    }, subItem.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 29\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 21\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"block text-gray-700 hover:text-primary-600 hover:bg-gray-50 px-3 py-2 text-base font-medium transition-colors\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, item.name, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/LanguageSwitcher.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/LanguageSwitcher.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst LanguageSwitcher = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { language, setLanguage } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const currentLanguage = _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.languages.find((lang)=>lang.code === language) || _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.languages[0];\n    const handleLanguageChange = (langCode)=>{\n        setLanguage(langCode);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-primary-600 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hidden sm:inline\",\n                        children: currentLanguage.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sm:hidden\",\n                        children: currentLanguage.flag\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: `h-3 w-3 transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1 max-h-64 overflow-y-auto\",\n                    children: _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleLanguageChange(lang.code),\n                            className: `w-full text-left flex items-center space-x-3 px-4 py-2 text-sm hover:bg-gray-50 transition-colors ${currentLanguage.code === lang.code ? \"bg-primary-50 text-primary-600\" : \"text-gray-700\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg\",\n                                    children: lang.flag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: lang.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, lang.code, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/products/ProductDetail.tsx":
/*!***************************************************!*\
  !*** ./src/components/products/ProductDetail.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst ProductDetail = ({ product, showBreadcrumb = false })=>{\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    // 兼容新旧数据结构\n    const allImages = product.images || [\n        {\n            url: product.image,\n            alt: \"Product Image\"\n        },\n        ...(product.gallery || []).map((url, index)=>({\n                url,\n                alt: `Product Image ${index + 2}`\n            }))\n    ].filter((img)=>img.url);\n    const nextImage = ()=>{\n        if (allImages.length > 0) {\n            setCurrentImageIndex((prev)=>(prev + 1) % allImages.length);\n        }\n    };\n    const prevImage = ()=>{\n        if (allImages.length > 0) {\n            setCurrentImageIndex((prev)=>(prev - 1 + allImages.length) % allImages.length);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            showBreadcrumb && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/products\",\n                        className: \"hover:text-primary-600 transition-colors\",\n                        children: t(\"nav.products\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"/\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-900\",\n                        children: t(product.nameKey)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative aspect-square rounded-xl overflow-hidden shadow-lg group\",\n                                children: [\n                                    allImages.length > 0 && allImages[currentImageIndex] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: allImages[currentImageIndex].url,\n                                        alt: allImages[currentImageIndex].alt || t(product.nameKey || product.name),\n                                        className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-gray-200 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"暂无图片\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    allImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: prevImage,\n                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: nextImage,\n                                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 9\n                            }, undefined),\n                            allImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 overflow-x-auto\",\n                                children: allImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentImageIndex(index),\n                                        className: `flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 ${index === currentImageIndex ? \"border-primary-500 ring-2 ring-primary-200\" : \"border-gray-200 hover:border-gray-300\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: image.url,\n                                            alt: image.alt || t(product.nameKey || product.name),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: product.nameKey ? t(product.nameKey) : product.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-lg leading-relaxed\",\n                                        children: product.descriptionKey ? t(product.descriptionKey) : product.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: t(\"productDetail.specifications\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: (product.specifications || product.specifications || []).map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: typeof spec === \"object\" && spec.key ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        t(spec.key),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 ml-2\",\n                                                                    children: spec.valueKey ? t(spec.valueKey) : spec.value\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: spec\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: t(\"productDetail.applications\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: (product.applications || product.applications || []).map((app, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-3 rounded-lg text-gray-700 text-sm font-medium\",\n                                                children: typeof app === \"object\" && app.key ? t(app.key) : app\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 9\n                            }, undefined),\n                            product.features && product.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: t(\"productDetail.features\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: product.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t(feature)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex-1 flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t(\"productDetail.getQuote\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border border-primary-600 text-primary-600 hover:bg-primary-50 px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex-1 flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t(\"productDetail.downloadSpec\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDetail);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/products/ProductDetail.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _public_locales_zh_common_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/locales/zh/common.json */ \"(ssr)/./public/locales/zh/common.json\");\n/* harmony import */ var _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../public/locales/en/common.json */ \"(ssr)/./public/locales/en/common.json\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage,languages auto */ \n\n// 直接导入翻译文件\n\n\n// 翻译数据存储 - 直接使用导入的数据\nconst translations = {\n    zh: _public_locales_zh_common_json__WEBPACK_IMPORTED_MODULE_2__,\n    en: _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__\n};\n// 获取嵌套对象的值\nconst getNestedValue = (obj, path)=>{\n    return path.split(\".\").reduce((current, key)=>{\n        return current && current[key] !== undefined ? current[key] : undefined;\n    }, obj);\n};\n// 创建上下文\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"zh\");\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用 useLayoutEffect 在渲染前同步读取 localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (false) {}\n    }, []);\n    // 服务端渲染时立即标记为已初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            setIsInitialized(true);\n        }\n    }, []);\n    // 设置语言并保存到 localStorage\n    const setLanguage = (lang)=>{\n        setLanguageState(lang);\n        if (false) {}\n    };\n    // 翻译函数\n    const t = (key)=>{\n        const translation = getNestedValue(translations[language], key);\n        if (!translation) {\n            console.warn(`Translation missing for key: ${key} in language: ${language}`);\n            return key // 返回键名作为后备\n            ;\n        }\n        return translation;\n    };\n    const value = {\n        language,\n        setLanguage,\n        t\n    };\n    // 在初始化完成前不渲染内容，避免闪烁\n    if (!isInitialized) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n// 使用语言上下文的Hook\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n// 导出语言列表供其他组件使用\nconst languages = [\n    {\n        code: \"zh\",\n        name: \"简体中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9c3cc20aed3a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbHhmYXN0ZW5lci13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz82Mjc3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOWMzY2MyMGFlZDNhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Roboto\",\"arguments\":[{\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-roboto\"}],\"variableName\":\"roboto\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Roboto\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-roboto\\\"}],\\\"variableName\\\":\\\"roboto\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./src/contexts/LanguageContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"联勋紧固件有限公司 - 专业紧固件制造商\",\n    description: \"联勋紧固件有限公司是一家专业的紧固件制造商，专注于设计和生产高品质紧固件。我们提供螺栓、螺母、螺钉、螺柱、垫圈等全系列紧固件产品。\",\n    keywords: \"紧固件,螺栓,螺母,螺钉,螺柱,垫圈,联勋紧固件\",\n    authors: [\n        {\n            name: \"联勋紧固件有限公司\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"联勋紧固件有限公司 - 专业紧固件制造商\",\n        description: \"联勋紧固件有限公司是一家专业的紧固件制造商，专注于设计和生产高品质紧固件。\",\n        type: \"website\",\n        locale: \"zh_CN\",\n        siteName: \"联勋紧固件有限公司\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/products/bolts/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/products/bolts/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\app\products\bolts\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   languages: () => (/* binding */ e2),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#useLanguage`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#languages`);


/***/ }),

/***/ "(ssr)/./public/locales/en/common.json":
/*!***************************************!*\
  !*** ./public/locales/en/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"nav":{"home":"Home","about":"About Us","products":"Products","technical":"Technical","contact":"Contact Us"},"hero":{"title1":"Professional Fastener Manufacturer","title2":"Quality Assurance, Precision Manufacturing","title3":"Global Service, Trustworthy","subtitle1":"Lianxun Fastener Co., Ltd","subtitle2":"10 Years Professional Experience","subtitle3":"Serving Global Customers","description1":"Specializing in the design, production and sales of high-quality fasteners, providing professional fastening system solutions for global customers","description2":"With advanced cold forging, hot forging workshops, heat treatment equipment, strict quality control system ensures product quality","description3":"Create benefits for customers, opportunities for employees, and value for society","cta1":"Learn More","cta2":"View Products","cta3":"Contact Us"},"about":{"title":"About Us","companyName":"Lianxun Fastener Co., Ltd","description":"Lianxun Fastener Co., Ltd is a professional Chinese fastener manufacturer specializing in the design and production of top-notch fasteners. Located in Yongnian District, Handan City - the \'Capital of Fasteners in China\', it covers a business area of 7,000 square meters including cold forging workshop, hot forging workshop, heat treatment workshop, warehouse and laboratory.","description2":"Currently, the company has more than 80 sets of production and testing equipment and more than 100 employees. We are committed to providing professional and efficient fastening system solutions for global customers. With corporate values of integrity, unity and innovation, we create benefits for customers, opportunities for employees, and value for society.","readMore":"Read More","stats":{"experience":"Years Experience","employees":"Professional Staff","equipment":"Production Equipment","facility":"Square Meters Facility"},"certification":"Quality Certification"},"products":{"title":"Products List","subtitle":"We provide a full range of high-quality fastener products to meet various industrial application needs","description":"We provide a full range of high-quality fastener products to meet various industrial application needs","viewMore":"View More Products","viewAll":"View All Products","bolts":"Bolts","nuts":"Nuts","screws":"Screws","studs":"Studs","washers":"Washers","categories":{"bolts":{"title":"Bolt Products","subtitle":"High-strength bolt products suitable for various engineering connections","hexBolt":{"name":"Hex Bolts","description":"Standard hex head bolts with strong versatility, suitable for various mechanical connections"},"flangeBolt":{"name":"Flange Bolts","description":"Bolts with flange plates to increase contact area and distribute loads"},"uBolt":{"name":"U Bolt","description":"Specialized U-bolts for pipe fixing, clamp connection, easy installation","shortDesc":"Specialized U-bolts for pipe fixing, clamp connection, easy installation"}},"nuts":{"title":"Nut Products","subtitle":"High-quality nut products, perfectly matched with bolts","hexNut":{"name":"Hex Nuts","description":"Standard hex nuts, used with bolts for reliable connections"},"flangeNut":{"name":"Flange Nuts","description":"Nuts with flange plates to increase contact area and distribute loads"}},"screws":{"title":"Screw Products","subtitle":"Precision screw products to meet various fastening needs","selfTapping":{"name":"Self-Tapping Screws","description":"Self-tapping screws that require no pre-drilling and directly tap threads for connection"},"machine":{"name":"Machine Screws","description":"Machine screws with precision threads, suitable for mechanical equipment"}},"studs":{"title":"Stud Products","subtitle":"Professional stud products providing reliable double-end connections","doubleEnd":{"name":"Double-End Studs","description":"Studs threaded at both ends for through connections"},"welding":{"name":"Welding Studs","description":"Studs specifically designed for welding connections, providing strong and reliable welds"}},"washers":{"title":"Washer Products","subtitle":"Quality washer products providing effective sealing and protection","flat":{"name":"Flat Washers","description":"Standard flat washers to increase contact area and prevent loosening"},"spring":{"name":"Spring Washers","description":"Elastic washers providing preload force to prevent bolt loosening"}}}},"specifications":{"spec":"Specification","strength":"Strength Grade","material":"Material","surface":"Surface Treatment","shape":"Shape","thickness":"Thickness","headType":"Head Type","weldingMethod":"Welding Method","length":"Length"},"applications":{"mechanical":"Mechanical Equipment","construction":"Construction Structure","automotive":"Automotive Industry","electrical":"Electrical Equipment","precision":"Precision Equipment","high-strength":"High-Strength Connection","pipe-fixing":"Pipe Fixing","cable-tray":"Cable Tray","equipment":"Equipment Installation","structure":"Structure Connection","thin-plate":"Thin Plate Connection","plastic":"Plastic Fixing","electronics":"Electronic Equipment","furniture":"Furniture Manufacturing","precision-machinery":"Precision Machinery","instruments":"Instruments","automation":"Automation Equipment","flange":"Flange Connection","pipeline":"Pipeline Connection","steel-structure":"Steel Structure","shipbuilding":"Shipbuilding","bridge":"Bridge Engineering","curtain-wall":"Curtain Wall","bolt-connection":"Bolt Connection","equipment-installation":"Equipment Installation","structural-fixing":"Structural Fixing"},"surfaceTreatments":{"zinc":"Zinc Plating","blackening":"Blackening","phosphating":"Phosphating","dacromet":"Dacromet","nickel":"Nickel Plating","stainless":"Stainless Steel Natural","passivation":"Passivation"},"hexBolt":{"spec":"M6-M36","strength":"4.8, 6.8, 8.8, 10.9, 12.9","material":"Q235, 35K, 45K, 40Cr","surface":"Zinc Plating, Blackening, Phosphating"},"flangeBolt":{"spec":"M6-M20","strength":"8.8, 10.9","material":"35K, 45K, 40Cr","surface":"Zinc Plating, Dacromet"},"uBolt":{"spec":"M8-M30","material":"Q235, 304 Stainless Steel","surface":"Zinc Plating, Stainless Steel Natural","shape":"Standard U-type, Extended U-type"},"product":{"structureBolts":{"name":"Structure Bolts","desc":"High-strength structural connection bolts for steel construction","description":"Structure bolts are high-strength fasteners specifically designed for steel structure connections, featuring excellent mechanical properties and corrosion resistance. Widely used in building steel structures, bridge engineering, tower structures and other important engineering projects.","specs":{"grade":"Strength Grade","size":"Size Range","length":"Length Range","material":"Material","surface":"Surface Treatment"},"apps":{"steel":"Steel Structure Buildings","bridge":"Bridge Engineering","tower":"Tower Structures","industrial":"Industrial Buildings"},"features":{"highStrength":"High strength design with strong load capacity","corrosionResistant":"Excellent corrosion resistance performance","precisionThreads":"Precision threads for reliable connections","qualityTested":"Strict quality testing, meets national standards"}},"shearWeldingStuds":{"name":"Shear Welding Studs","desc":"Professional welding studs providing reliable shear connections","description":"Shear welding studs are connectors fixed to steel structures through arc welding, mainly used to transfer shear forces in steel-concrete composite structures. Features fast welding, reliable connections, and convenient construction.","specs":{"diameter":"Diameter","length":"Length","material":"Material","weldTime":"Welding Time"},"apps":{"composite":"Steel-Concrete Composite Structures","deck":"Steel Bar Truss Floor Deck","precast":"Precast Component Connections"},"features":{"fastWelding":"Fast welding with high efficiency","strongShear":"Strong shear load capacity","precisePosition":"Precise positioning with stable quality"}},"hexBolts":{"name":"Hex Bolts","desc":"Standard hex head bolts widely used in mechanical connections","description":"Hex bolts are one of the most commonly used fasteners, featuring high standardization, strong versatility, and easy installation. Suitable for connections and fixings in various mechanical equipment, building structures, automotive manufacturing and other fields.","specs":{"grade":"Strength Grade","size":"Size Range","length":"Length Range","material":"Material"},"apps":{"machinery":"Mechanical Equipment","automotive":"Automotive Manufacturing","construction":"Construction Engineering","equipment":"Equipment Installation"},"features":{"versatile":"Strong versatility with wide applications","standardized":"Standardized production with good interchangeability","reliable":"Reliable connections with stable performance"}},"hexFlangeBolts":{"name":"Hex Flange Bolts","desc":"Hex bolts with flange for increased contact area"},"carriageBolts":{"name":"Carriage Bolts","desc":"Square neck bolts preventing rotation, suitable for wood connections"},"eyeBolts":{"name":"Eye Bolts","desc":"Bolts with ring head for lifting and traction applications"},"foundationAnchorBolts":{"name":"Foundation Anchor Bolts","desc":"Anchor bolts for equipment foundation fixing"},"uBolts":{"name":"U-Bolts","desc":"U-shaped bolts for pipe and round object fixing"}},"productDetail":{"specifications":"Technical Specifications","applications":"Applications","features":"Product Features","relatedProducts":"Related Products","getQuote":"Get Quote","downloadSpec":"Download Datasheet","backToProducts":"Back to Products","gallery":"Product Gallery","overview":"Product Overview"},"culture":{"title":"Corporate Culture","subtitle":"Our core values guide our development direction and shape our corporate spirit","mission":"Corporate Mission","vision":"Corporate Vision","values":"Corporate Values","missionDesc":"Provide professional and efficient fastening system solutions for customers worldwide","visionDesc":"Create benefits for customers, opportunities for employees, and value for society","valuesDesc":"Honesty, Trustworthiness, Unity, Innovation","quality":{"title":"Quality Commitment","description":"We strictly implement the ISO 9001:2015 quality management system standards. From raw material procurement to finished product delivery, every link undergoes strict quality control. Our laboratory is equipped with advanced testing equipment to ensure that every product meets international standards and customer requirements.","certification":"ISO 9001:2015 Quality Management System Certification","materials":"Strict raw material inspection standards","process":"Perfect production process control","testing":"Comprehensive finished product testing system"}},"technical":{"title":"Technical Support","subtitle":"Professional technical documentation and solutions","description":"We provide comprehensive technical support and professional guidance to help you choose the most suitable fastener solutions","featuredArticle":"Featured Article","technicalDocs":"Technical Documentation","readFull":"Read Full Article","readMore":"Read More","readTime":"read","downloadTitle":"Technical Documentation Download","downloadDesc":"We provide complete product technical documentation and specification downloads, including CAD drawings, technical parameter tables, installation guides, etc.","downloadBtn":"Download Technical Documentation Package","categories":{"guide":"Technical Guide","standard":"Standards & Specifications","data":"Technical Data","calculation":"Calculation Methods","analysis":"Case Analysis"},"articles":{"coarseVsFine":{"title":"Comparison of Coarse and Fine Threads","excerpt":"Are coarse threads better or fine threads better? This is a question we often hear about inserts and external threaded fasteners. This article will analyze the characteristics, application scenarios and selection criteria of both thread types.","date":"January 6, 2025"},"surfaceTreatment":{"title":"European Surface Treatment Codes and Standards","excerpt":"Detailed introduction to the code system and technical specifications for fastener surface treatment in European standards, including standard requirements for various surface treatment processes such as galvanizing, blackening, and phosphating.","date":"January 6, 2025"},"hardnessConversion":{"title":"Hardness Conversion Chart","excerpt":"Approximate conversion values for steel Rockwell C hardness, including comparison relationships between HRC, HV, HB and other hardness standards, as well as the application range of various hardness testing methods.","date":"January 6, 2025"},"materialSelection":{"title":"Fastener Material Selection Guide","excerpt":"How to select appropriate fastener materials based on application environment, including the characteristics and applicable scenarios of carbon steel, alloy steel, stainless steel and other materials.","date":"January 5, 2025"},"boltPreload":{"title":"Bolt Preload Calculation Methods","excerpt":"Detailed introduction to bolt preload calculation methods, influencing factors and control standards to ensure connection reliability and safety.","date":"January 4, 2025"},"failureAnalysis":{"title":"Fastener Failure Analysis Cases","excerpt":"Analysis of fastener failure causes through actual cases, including preventive measures for common problems such as fatigue fracture, corrosion failure, and overload fracture.","date":"January 3, 2025"}}},"contact":{"title":"Contact Us","subtitle":"We look forward to working with you to provide professional fastener solutions","info":{"title":"Contact Information","description":"We are always ready to provide you with professional services and support. Whether you have any questions about our products or need customized fastener solutions, please feel free to contact us.","address":"Company Address","phone":"Phone","mobile":"Mobile/WeChat","email":"Email","salesEmail":"Sales Email","workTime":"Working Hours","location":"Company Location","mapLoading":"Loading map...","businessHours":"Business Hours","monday":"Monday - Friday","saturday":"Saturday","sunday":"Sunday","closed":"Closed"},"form":{"title":"Send Message","name":"Name","email":"Email","phone":"Phone","company":"Company Name","subject":"Subject","message":"Message","required":"*","placeholders":{"name":"Please enter your name","email":"Please enter your email","phone":"Please enter your phone","company":"Please enter your company name","subject":"Please select a subject","message":"Please describe your requirements or questions in detail..."},"subjects":{"select":"Please select a subject","product":"Product Inquiry","technical":"Technical Support","quotation":"Quotation Request","partnership":"Partnership","other":"Other"},"submit":"Send Message","submitting":"Sending...","success":{"title":"Message sent successfully!","message":"Thank you for your inquiry, we will reply within 24 hours."}}},"footer":{"address":"Yongnian District, Handan City, Hebei Province, China","description":"Professional industrial fastener manufacturer specializing in the design and production of structural fasteners of all kinds. Over 10 years\' experience in providing our customers with high quality materials at competitive prices and best service.","productCategories":"Product Categories","quickLinks":"Quick Links","getInTouch":"Get In Touch","copyright":"© 2024 Lianxun Fastener Co., Ltd. All Rights Reserved."}}');

/***/ }),

/***/ "(ssr)/./public/locales/zh/common.json":
/*!***************************************!*\
  !*** ./public/locales/zh/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"nav":{"home":"首页","about":"关于我们","products":"产品中心","technical":"技术支持","contact":"联系我们"},"hero":{"title1":"专业紧固件制造商","title2":"品质保证 精工制造","title3":"全球服务 值得信赖","subtitle1":"联勋紧固件有限公司","subtitle2":"10年专业经验","subtitle3":"服务全球客户","description1":"专注于高品质紧固件的设计、生产和销售，为全球客户提供专业的紧固系统解决方案","description2":"拥有先进的冷锻、热锻车间，热处理设备，严格的质量控制体系确保产品品质","description3":"为客户创造效益，为员工创造机会，为社会创造价值","cta1":"了解更多","cta2":"查看产品","cta3":"联系我们"},"about":{"title":"关于我们","companyName":"联勋紧固件有限公司","description":"联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。公司位于\\"中国紧固件之都\\"——河北省邯郸市永年区，占地面积7000平方米，包括冷锻车间、热锻车间、热处理车间、仓库和实验室。","description2":"目前，公司拥有80多套生产和检测设备，员工100多人。我们致力于为全球客户提供专业高效的紧固系统解决方案，以诚信、团结、创新的企业价值观，为客户创造效益，为员工创造机会，为社会创造价值。","readMore":"了解更多","stats":{"experience":"年专业经验","employees":"专业员工","equipment":"生产设备","facility":"平方米厂房"},"certification":"质量认证"},"products":{"title":"产品列表","subtitle":"我们提供全系列高品质紧固件产品，满足各种工业应用需求","description":"我们提供全系列高品质紧固件产品，满足各种工业应用需求","viewMore":"查看更多产品","viewAll":"查看全部产品","bolts":"螺栓","nuts":"螺母","screws":"螺钉","studs":"螺柱","washers":"垫圈","categories":{"bolts":{"title":"螺栓产品","subtitle":"高强度螺栓产品，适用于各种工程连接","hexBolt":{"name":"六角螺栓","description":"标准六角头螺栓，通用性强，适用于各种机械连接"},"flangeBolt":{"name":"法兰螺栓","description":"带法兰盘的螺栓，增大接触面积，分散载荷"},"uBolt":{"name":"U型螺栓","description":"管道固定专用U型螺栓，抱箍连接，安装方便","shortDesc":"管道固定专用U型螺栓，抱箍连接，安装方便"}},"nuts":{"title":"螺母产品","subtitle":"高品质螺母产品，与螺栓完美配套","hexNut":{"name":"六角螺母","description":"标准六角螺母，与螺栓配套使用，连接可靠"},"flangeNut":{"name":"法兰螺母","description":"带法兰盘的螺母，增大接触面积，分散载荷"}},"screws":{"title":"螺钉产品","subtitle":"精密螺钉产品，满足各种固定需求","selfTapping":{"name":"自攻螺钉","description":"自攻螺钉，无需预钻孔，直接攻丝连接"},"machine":{"name":"机器螺钉","description":"机器螺钉，精密螺纹，适用于机械设备"}},"studs":{"title":"螺柱产品","subtitle":"专业螺柱产品，提供可靠的双端连接","doubleEnd":{"name":"双头螺柱","description":"两端均为螺纹的螺柱，用于穿透连接"},"welding":{"name":"焊接螺柱","description":"专用于焊接连接的螺柱，焊接牢固可靠"}},"washers":{"title":"垫圈产品","subtitle":"优质垫圈产品，提供有效的密封和保护","flat":{"name":"平垫圈","description":"标准平垫圈，增大接触面积，防止松动"},"spring":{"name":"弹簧垫圈","description":"弹性垫圈，提供预紧力，防止螺栓松动"}}}},"specifications":{"spec":"规格","strength":"强度等级","material":"材质","surface":"表面处理","shape":"形状","thickness":"厚度","headType":"头型","weldingMethod":"焊接方式","length":"长度"},"applications":{"mechanical":"机械设备","construction":"建筑结构","automotive":"汽车工业","electrical":"电力设备","precision":"精密设备","high-strength":"高强度连接","pipe-fixing":"管道固定","cable-tray":"电缆桥架","equipment":"设备安装","structure":"结构连接","thin-plate":"薄板连接","plastic":"塑料固定","electronics":"电子设备","furniture":"家具制造","precision-machinery":"精密机械","instruments":"仪器仪表","automation":"自动化设备","flange":"法兰连接","pipeline":"管道连接","steel-structure":"钢结构","shipbuilding":"船舶制造","bridge":"桥梁工程","curtain-wall":"建筑幕墙","bolt-connection":"螺栓连接","equipment-installation":"设备安装","structural-fixing":"结构固定"},"surfaceTreatments":{"zinc":"镀锌","blackening":"发黑","phosphating":"磷化","dacromet":"达克罗","nickel":"镀镍","stainless":"不锈钢本色","passivation":"钝化"},"hexBolt":{"spec":"M6-M36","strength":"4.8, 6.8, 8.8, 10.9, 12.9","material":"Q235, 35K, 45K, 40Cr","surface":"镀锌、发黑、磷化"},"flangeBolt":{"spec":"M6-M20","strength":"8.8, 10.9","material":"35K, 45K, 40Cr","surface":"镀锌、达克罗"},"uBolt":{"spec":"M8-M30","material":"Q235, 304不锈钢","surface":"镀锌、不锈钢本色","shape":"标准U型、加长U型"},"product":{"structureBolts":{"name":"结构螺栓","desc":"高强度结构连接螺栓，适用于钢结构建筑","description":"结构螺栓是专为钢结构连接设计的高强度紧固件，具有优异的机械性能和耐腐蚀性能。广泛应用于建筑钢结构、桥梁工程、塔架结构等重要工程项目中。","specs":{"grade":"强度等级","size":"规格尺寸","length":"长度范围","material":"材质","surface":"表面处理"},"apps":{"steel":"钢结构建筑","bridge":"桥梁工程","tower":"塔架结构","industrial":"工业厂房"},"features":{"highStrength":"高强度设计，承载能力强","corrosionResistant":"优异的防腐蚀性能","precisionThreads":"精密螺纹，连接可靠","qualityTested":"严格质量检测，符合国标"}},"shearWeldingStuds":{"name":"剪切焊接螺柱","desc":"专业焊接螺柱，提供可靠的剪切连接","description":"剪切焊接螺柱是一种通过电弧焊接固定在钢结构上的连接件，主要用于钢-混凝土组合结构中传递剪力。具有焊接快速、连接可靠、施工方便等特点。","specs":{"diameter":"直径","length":"长度","material":"材质","weldTime":"焊接时间"},"apps":{"composite":"钢混组合结构","deck":"钢筋桁架楼承板","precast":"预制构件连接"},"features":{"fastWelding":"快速焊接，效率高","strongShear":"剪切承载力强","precisePosition":"定位精确，质量稳定"}},"hexBolts":{"name":"六角螺栓","desc":"标准六角头螺栓，广泛应用于机械连接","description":"六角螺栓是最常用的紧固件之一，具有标准化程度高、通用性强、安装方便等特点。适用于各种机械设备、建筑结构、汽车制造等领域的连接固定。","specs":{"grade":"强度等级","size":"规格尺寸","length":"长度范围","material":"材质"},"apps":{"machinery":"机械设备","automotive":"汽车制造","construction":"建筑工程","equipment":"设备安装"},"features":{"versatile":"通用性强，应用广泛","standardized":"标准化生产，互换性好","reliable":"连接可靠，性能稳定"}},"hexFlangeBolts":{"name":"六角法兰螺栓","desc":"带法兰盘的六角螺栓，增大接触面积"},"carriageBolts":{"name":"马车螺栓","desc":"方颈螺栓，防止转动，适用于木材连接"},"eyeBolts":{"name":"吊环螺栓","desc":"带环形头部的螺栓，用于起重和牵引"},"foundationAnchorBolts":{"name":"地脚锚栓","desc":"用于设备固定的地脚螺栓"},"uBolts":{"name":"U型螺栓","desc":"U形螺栓，用于管道和圆形物体固定"}},"productDetail":{"specifications":"技术规格","applications":"应用领域","features":"产品特点","relatedProducts":"相关产品","getQuote":"获取报价","downloadSpec":"下载规格书","backToProducts":"返回产品列表","gallery":"产品图册","overview":"产品概述"},"culture":{"title":"企业文化","subtitle":"我们的核心价值观指引着我们的发展方向，塑造着我们的企业精神","mission":"企业使命","vision":"企业愿景","values":"企业价值观","missionDesc":"为全球客户提供专业高效的紧固系统解决方案","visionDesc":"为客户创造效益，为员工创造机会，为社会创造价值","valuesDesc":"诚信、守信、团结、创新","quality":{"title":"质量承诺","description":"我们严格按照ISO 9001:2015质量管理体系标准执行，从原材料采购到成品出厂，每一个环节都经过严格的质量控制。我们的实验室配备了先进的检测设备，确保每一件产品都符合国际标准和客户要求。","certification":"ISO 9001:2015 质量管理体系认证","materials":"严格的原材料检验标准","process":"完善的生产过程控制","testing":"全面的成品检测体系"}},"technical":{"title":"技术支持","subtitle":"专业的技术文档和解决方案","description":"我们提供全面的技术支持和专业指导，帮助您选择最适合的紧固件解决方案","featuredArticle":"精选文章","technicalDocs":"技术文档","readFull":"阅读全文","readMore":"阅读更多","readTime":"阅读","downloadTitle":"技术资料下载","downloadDesc":"我们提供完整的产品技术资料和规格书下载，包括CAD图纸、技术参数表、安装指南等。","downloadBtn":"下载技术资料包","categories":{"guide":"技术指南","standard":"标准规范","data":"技术资料","calculation":"计算方法","analysis":"案例分析"},"articles":{"coarseVsFine":{"title":"粗牙螺纹与细牙螺纹的比较","excerpt":"粗牙螺纹好还是细牙螺纹好？这是我们公司经常听到的关于嵌件和外螺纹紧固件的问题。本文将详细分析两种螺纹的特点、应用场景和选择标准。","date":"2025年1月6日"},"surfaceTreatment":{"title":"欧洲表面处理代码和规范","excerpt":"详细介绍欧洲标准中关于紧固件表面处理的代码体系和技术规范，包括镀锌、发黑、磷化等各种表面处理工艺的标准要求。","date":"2025年1月6日"},"hardnessConversion":{"title":"硬度换算对照表","excerpt":"钢材洛氏C硬度的近似换算值，包括HRC、HV、HB等不同硬度标准的对照关系，以及各种硬度测试方法的应用范围。","date":"2025年1月6日"},"materialSelection":{"title":"紧固件材料选择指南","excerpt":"如何根据应用环境选择合适的紧固件材料，包括碳钢、合金钢、不锈钢等材料的特性和适用场景。","date":"2025年1月5日"},"boltPreload":{"title":"螺栓预紧力计算方法","excerpt":"详细介绍螺栓预紧力的计算方法、影响因素和控制标准，确保连接的可靠性和安全性。","date":"2025年1月4日"},"failureAnalysis":{"title":"紧固件失效分析案例","excerpt":"通过实际案例分析紧固件失效的原因，包括疲劳断裂、腐蚀失效、过载断裂等常见问题的预防措施。","date":"2025年1月3日"}}},"contact":{"title":"联系我们","subtitle":"我们期待与您合作，为您提供专业的紧固件解决方案","info":{"title":"联系信息","description":"我们随时准备为您提供专业的服务和支持。无论您有任何关于产品的问题，或者需要定制化的紧固件解决方案，请随时与我们联系。","address":"公司地址","phone":"联系电话","mobile":"手机/微信","email":"邮箱地址","salesEmail":"销售邮箱","workTime":"工作时间","location":"公司位置","mapLoading":"地图加载中...","businessHours":"营业时间","monday":"周一 - 周五","saturday":"周六","sunday":"周日","closed":"休息"},"form":{"title":"发送消息","name":"姓名","email":"邮箱","phone":"电话","company":"公司名称","subject":"咨询主题","message":"详细信息","required":"*","placeholders":{"name":"请输入您的姓名","email":"请输入您的邮箱","phone":"请输入您的电话","company":"请输入您的公司名称","subject":"请选择咨询主题","message":"请详细描述您的需求或问题..."},"subjects":{"select":"请选择咨询主题","product":"产品咨询","technical":"技术支持","quotation":"报价询问","partnership":"合作洽谈","other":"其他"},"submit":"发送消息","submitting":"发送中...","success":{"title":"消息发送成功！","message":"感谢您的咨询，我们会在24小时内回复您。"}}},"footer":{"address":"中国河北省邯郸市永年区刘营镇五里村工业区","description":"专业的工业紧固件制造商，专注于各类结构紧固件的设计与生产。拥有超过10年的经验，为客户提供高品质材料、具有竞争力的价格和最佳服务。","productCategories":"产品分类","quickLinks":"快速链接","getInTouch":"联系我们","copyright":"© 2024 联勋紧固件有限公司. 保留所有权利."}}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fbolts%2Fpage&page=%2Fproducts%2Fbolts%2Fpage&appPaths=%2Fproducts%2Fbolts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fbolts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();