'use client'

import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import ProductDetail from '@/components/products/ProductDetail'
import { useLanguage } from '@/contexts/LanguageContext'

export default function NutsPage() {
  const { t } = useLanguage()

  const nutProducts = [
    {
      id: 1,
      name: t('products.categories.nuts.hexNut.name'),
      description: t('products.categories.nuts.hexNut.description'),
      specifications: [
        '规格: M6-M36',
        '强度等级: 4, 6, 8, 10, 12',
        '材质: Q235, 35K, 45K',
        '表面处理: 镀锌、发黑、磷化'
      ],
      applications: [
        '机械设备',
        '建筑结构',
        '汽车制造',
        '电力设备'
      ],
      image: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gallery: [
        'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      ]
    },
    {
      id: 2,
      name: t('products.categories.nuts.flangeNut.name'),
      description: t('products.categories.nuts.flangeNut.description'),
      specifications: [
        '规格: M6-M20',
        '强度等级: 8, 10',
        '材质: 35K, 45K',
        '表面处理: 镀锌、达克罗'
      ],
      applications: [
        '汽车工业',
        '机械制造',
        '精密设备',
        '航空航天'
      ],
      image: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gallery: [
        'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      ]
    }
  ]

  return (
    <main className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary-600 to-primary-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('products.categories.nuts.title')}
          </h1>
          <p className="text-xl text-primary-100 max-w-3xl mx-auto">
            {t('products.categories.nuts.subtitle')}
          </p>
        </div>
      </section>

      {/* Products List */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {nutProducts.map((product) => (
              <ProductDetail key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
