"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return Router;\n    },\n    matchesMiddleware: function() {\n        return matchesMiddleware;\n    },\n    createKey: function() {\n        return createKey;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"./node_modules/next/dist/client/route-loader.js\");\nconst _script = __webpack_require__(/*! ../../../client/script */ \"./node_modules/next/dist/client/script.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"./node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../mitt */ \"./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _utils = __webpack_require__(/*! ../utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nconst _resolverewrites = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"?506d\"));\nconst _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nconst _formaturl = __webpack_require__(/*! ./utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"./node_modules/next/dist/client/detect-domain-locale.js\");\nconst _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"./node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nconst _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"./node_modules/next/dist/client/remove-locale.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"./node_modules/next/dist/client/remove-base-path.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nconst _resolvehref = __webpack_require__(/*! ../../../client/resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"./node_modules/next/dist/lib/is-api-route.js\");\nconst _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nconst _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nconst _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"./node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nconst _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _isbot = __webpack_require__(/*! ./utils/is-bot */ \"./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _omit = __webpack_require__(/*! ./utils/omit */ \"./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nfunction buildCancellationError() {\n    return Object.assign(new Error(\"Route Cancelled\"), {\n        cancelled: true\n    });\n}\nasync function matchesMiddleware(options) {\n    const matchers = await Promise.resolve(options.router.pageLoader.getMiddleware());\n    if (!matchers) return false;\n    const { pathname: asPathname } = (0, _parsepath.parsePath)(options.asPath);\n    // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n    const cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n    const asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n    // Check only path match on client. Matching \"has\" should be done on server\n    // where we can access more info such as headers, HttpOnly cookie, etc.\n    return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils.getLocationOrigin)();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, url, true);\n    const origin = (0, _utils.getLocationOrigin)();\n    const hrefWasAbsolute = resolvedHref.startsWith(origin);\n    const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n    const preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n    if (cleanPathname === \"/404\" || cleanPathname === \"/_error\") {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get(\"x-nextjs-rewrite\");\n    let rewriteTarget = rewriteHeader || response.headers.get(\"x-nextjs-matched-path\");\n    const matchedPath = response.headers.get(\"x-matched-path\");\n    if (matchedPath && !rewriteTarget && !matchedPath.includes(\"__next_data_catchall\") && !matchedPath.includes(\"/_error\") && !matchedPath.includes(\"/404\")) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith(\"/\") || undefined) {\n            const parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n            const pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)()\n            ]).then((param)=>{\n                let [pages, { __rewrites: rewrites }] = param;\n                let as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n                        nextConfig:  false ? 0 : nextConfig,\n                        parseData: true\n                    });\n                    as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (false) {} else if (!pages.includes(fsPathname)) {\n                    const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n                    if (resolvedPathname !== fsPathname) {\n                        fsPathname = resolvedPathname;\n                    }\n                }\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n                    const matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: \"rewrite\",\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsepath.parsePath)(source);\n        const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n            ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                nextConfig,\n                parseData: true\n            }),\n            defaultLocale: options.router.defaultLocale,\n            buildId: \"\"\n        });\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: \"\" + pathname + src.query + src.hash\n        });\n    }\n    const redirectTarget = response.headers.get(\"x-nextjs-redirect\");\n    if (redirectTarget) {\n        if (redirectTarget.startsWith(\"/\")) {\n            const src = (0, _parsepath.parsePath)(redirectTarget);\n            const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n                ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                    nextConfig,\n                    parseData: true\n                }),\n                defaultLocale: options.router.defaultLocale,\n                buildId: \"\"\n            });\n            return Promise.resolve({\n                type: \"redirect-internal\",\n                newAs: \"\" + pathname + src.query + src.hash,\n                newUrl: \"\" + pathname + src.query + src.hash\n            });\n        }\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: \"next\"\n    });\n}\nasync function withMiddlewareEffects(options) {\n    const matches = await matchesMiddleware(options);\n    if (!matches || !options.fetchData) {\n        return null;\n    }\n    try {\n        const data = await options.fetchData();\n        const effect = await getMiddlewareData(data.dataHref, data.response, options);\n        return {\n            dataHref: data.dataHref,\n            json: data.json,\n            response: data.response,\n            text: data.text,\n            cacheKey: data.cacheKey,\n            effect\n        };\n    } catch (e) {\n        /**\n     * TODO: Revisit this in the future.\n     * For now we will not consider middleware data errors to be fatal.\n     * maybe we should revisit in the future.\n     */ return null;\n    }\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol(\"SSG_DATA_NOT_FOUND\");\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: \"same-origin\",\n        method: options.method || \"GET\",\n        headers: Object.assign({}, options.headers, {\n            \"x-nextjs-data\": \"1\"\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    let { dataHref, inflightCache, isPrefetch, hasMiddleware, isServerRender, parseJSON, persistCache, isBackground, unstable_skipClientCache } = param;\n    const { href: cacheKey } = new URL(dataHref, window.location.href);\n    var _params_method;\n    const getData = (params)=>fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: \"prefetch\"\n            } : {}, isPrefetch && hasMiddleware ? {\n                \"x-middleware-prefetch\": \"1\"\n            } : {}),\n            method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : \"GET\"\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === \"HEAD\") {\n                return {\n                    dataHref,\n                    response,\n                    text: \"\",\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var _tryToParseAsJSON;\n                        if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = new Error(\"Failed to load static props\");\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeloader.markAssetError)(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== \"production\" || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === \"Failed to fetch\" || // firefox\n            err.message === \"NetworkError when attempting to fetch resource.\" || // safari\n            err.message === \"Load failed\") {\n                (0, _routeloader.markAssetError)(err);\n            }\n            throw err;\n        });\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            inflightCache[cacheKey] = Promise.resolve(data);\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: \"HEAD\"\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    let { url, router } = param;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n        throw new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href);\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = (param)=>{\n    let { route, router } = param;\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = new Error('Abort fetching component for route: \"' + route + '\"');\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Go forward in history\n   */ forward() {\n        window.history.forward();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as, options) {\n        if (options === void 0) options = {};\n        if (false) {}\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"pushState\", url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as, options) {\n        if (options === void 0) options = {};\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"replaceState\", url, as, options);\n    }\n    async _bfl(as, resolvedAs, locale, skipNavigate) {\n        if (true) {\n            let matchesBflStatic = false;\n            let matchesBflDynamic = false;\n            for (const curAs of [\n                as,\n                resolvedAs\n            ]){\n                if (curAs) {\n                    const asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, \"http://n\").pathname);\n                    const asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || this.locale));\n                    if (asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(this.asPath, \"http://n\").pathname)) {\n                        var _this__bfl_s, _this__bfl_s1;\n                        matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n                        for (const normalizedAS of [\n                            asNoSlash,\n                            asNoSlashLocale\n                        ]){\n                            // if any sub-path of as matches a dynamic filter path\n                            // it should be hard navigated\n                            const curAsParts = normalizedAS.split(\"/\");\n                            for(let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                var _this__bfl_d;\n                                const currentPart = curAsParts.slice(0, i).join(\"/\");\n                                if (currentPart && ((_this__bfl_d = this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                                    matchesBflDynamic = true;\n                                    break;\n                                }\n                            }\n                        }\n                        // if the client router filter is matched then we trigger\n                        // a hard navigation\n                        if (matchesBflStatic || matchesBflDynamic) {\n                            if (skipNavigate) {\n                                return true;\n                            }\n                            handleHardNavigation({\n                                url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                                router: this\n                            });\n                            return new Promise(()=>{});\n                        }\n                    }\n                }\n            }\n        }\n        return false;\n    }\n    async change(method, url, as, options, forcedScroll) {\n        var _this_components_pathname;\n        if (!(0, _islocalurl.isLocalURL)(url)) {\n            handleHardNavigation({\n                url,\n                router: this\n            });\n            return false;\n        }\n        // WARNING: `_h` is an internal option for handing Next.js client-side\n        // hydration. Your app should _never_ use this property. It may change at\n        // any time without notice.\n        const isQueryUpdating = options._h === 1;\n        if (!isQueryUpdating && !options.shallow) {\n            await this._bfl(as, undefined, options.locale);\n        }\n        let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n        const nextState = {\n            ...this.state\n        };\n        // for static pages with query params in the URL we delay\n        // marking the router ready until after the query is updated\n        // or a navigation has occurred\n        const readyStateChange = this.isReady !== true;\n        this.isReady = true;\n        const isSsr = this.isSsr;\n        if (!isQueryUpdating) {\n            this.isSsr = false;\n        }\n        // if a route transition is already in progress before\n        // the query updating is triggered ignore query updating\n        if (isQueryUpdating && this.clc) {\n            return false;\n        }\n        const prevLocale = nextState.locale;\n        if (false) { var _this_locales; }\n        // marking route changes as a navigation start entry\n        if (_utils.ST) {\n            performance.mark(\"routeChange\");\n        }\n        const { shallow = false, scroll = true } = options;\n        const routeProps = {\n            shallow\n        };\n        if (this._inFlightRoute && this.clc) {\n            if (!isSsr) {\n                Router.events.emit(\"routeChangeError\", buildCancellationError(), this._inFlightRoute, routeProps);\n            }\n            this.clc();\n            this.clc = null;\n        }\n        as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, this.defaultLocale));\n        const cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n        this._inFlightRoute = as;\n        const localeChange = prevLocale !== nextState.locale;\n        // If the url change is only related to a hash change\n        // We should not proceed. We should only change the state.\n        if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n            nextState.asPath = cleanedAs;\n            Router.events.emit(\"hashChangeStart\", as, routeProps);\n            // TODO: do we need the resolved href when only a hash change?\n            this.changeState(method, url, as, {\n                ...options,\n                scroll: false\n            });\n            if (scroll) {\n                this.scrollToHash(cleanedAs);\n            }\n            try {\n                await this.set(nextState, this.components[nextState.route], null);\n            } catch (err) {\n                if ((0, _iserror.default)(err) && err.cancelled) {\n                    Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                }\n                throw err;\n            }\n            Router.events.emit(\"hashChangeComplete\", as, routeProps);\n            return true;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        let { pathname, query } = parsed;\n        // if we detected the path as app route during prefetching\n        // trigger hard navigation\n        if ((_this_components_pathname = this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return new Promise(()=>{});\n        }\n        // The build manifest needs to be loaded before auto-static dynamic pages\n        // get their query parameters to allow ensuring they can be parsed properly\n        // when rewritten to\n        let pages, rewrites;\n        try {\n            [pages, { __rewrites: rewrites }] = await Promise.all([\n                this.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)(),\n                this.pageLoader.getMiddleware()\n            ]);\n        } catch (err) {\n            // If we fail to resolve the page list or client-build manifest, we must\n            // do a server-side transition:\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        // If asked to change the current URL we should reload the current page\n        // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n        // We also need to set the method = replaceState always\n        // as this should not go into the history (That's how browsers work)\n        // We should compare the new asPath to the current asPath, not the url\n        if (!this.urlIsNew(cleanedAs) && !localeChange) {\n            method = \"replaceState\";\n        }\n        // we need to resolve the as value using rewrites for dynamic SSG\n        // pages to allow building the data URL correctly\n        let resolvedAs = as;\n        // url and as should always be prefixed with basePath by this\n        // point by either next/link or router.push/replace so strip the\n        // basePath from the pathname to match the pages dir 1-to-1\n        pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n        let route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        const parsedAsPathname = as.startsWith(\"/\") && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n        const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n        // we don't attempt resolve asPath when we need to execute\n        // middleware as the resolving will occur server-side\n        const isMiddlewareMatch = !options.shallow && await matchesMiddleware({\n            asPath: as,\n            locale: nextState.locale,\n            router: this\n        });\n        if (isQueryUpdating && isMiddlewareMatch) {\n            shouldResolveHref = false;\n        }\n        if (shouldResolveHref && pathname !== \"/_error\") {\n            options._shouldResolveHref = true;\n            if (false) {} else {\n                parsed.pathname = resolveDynamicRoute(pathname, pages);\n                if (parsed.pathname !== pathname) {\n                    pathname = parsed.pathname;\n                    parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                    if (!isMiddlewareMatch) {\n                        url = (0, _formaturl.formatWithValidation)(parsed);\n                    }\n                }\n            }\n        }\n        if (!(0, _islocalurl.isLocalURL)(as)) {\n            if (true) {\n                throw new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\");\n            }\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n        route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        let routeMatch = false;\n        if ((0, _isdynamic.isDynamicRoute)(route)) {\n            const parsedAs = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n            const asPathname = parsedAs.pathname;\n            const routeRegex = (0, _routeregex.getRouteRegex)(route);\n            routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n            const shouldInterpolate = route === asPathname;\n            const interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n            if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param] && !routeRegex.groups[param].optional);\n                if (missingParams.length > 0 && !isMiddlewareMatch) {\n                    if (true) {\n                        console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(\", \") + \" in the `href`'s `query`\"));\n                    }\n                    throw new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(\", \") + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? \"href-interpolation-failed\" : \"incompatible-href-as\")));\n                }\n            } else if (shouldInterpolate) {\n                as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs, {\n                    pathname: interpolatedAs.result,\n                    query: (0, _omit.omit)(query, interpolatedAs.params)\n                }));\n            } else {\n                // Merge params into `query`, overwriting any specified in search\n                Object.assign(query, routeMatch);\n            }\n        }\n        if (!isQueryUpdating) {\n            Router.events.emit(\"routeChangeStart\", as, routeProps);\n        }\n        const isErrorRoute = this.pathname === \"/404\" || this.pathname === \"/_error\";\n        try {\n            var _self___NEXT_DATA___props_pageProps, _self___NEXT_DATA___props, _routeInfo_props;\n            let routeInfo = await this.getRouteInfo({\n                route,\n                pathname,\n                query,\n                as,\n                resolvedAs,\n                routeProps,\n                locale: nextState.locale,\n                isPreview: nextState.isPreview,\n                hasMiddleware: isMiddlewareMatch,\n                unstable_skipClientCache: options.unstable_skipClientCache,\n                isQueryUpdating: isQueryUpdating && !this.isFallback,\n                isMiddlewareRewrite\n            });\n            if (!isQueryUpdating && !options.shallow) {\n                await this._bfl(as, \"resolvedAs\" in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n            }\n            if (\"route\" in routeInfo && isMiddlewareMatch) {\n                pathname = routeInfo.route || route;\n                route = pathname;\n                if (!routeProps.shallow) {\n                    query = Object.assign({}, routeInfo.query || {}, query);\n                }\n                const cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n                if (routeMatch && pathname !== cleanedParsedPathname) {\n                    Object.keys(routeMatch).forEach((key)=>{\n                        if (routeMatch && query[key] === routeMatch[key]) {\n                            delete query[key];\n                        }\n                    });\n                }\n                if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n                    const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n                    let rewriteAs = prefixedAs;\n                    if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n                        rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n                    }\n                    if (false) {}\n                    const routeRegex = (0, _routeregex.getRouteRegex)(pathname);\n                    const curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(new URL(rewriteAs, location.href).pathname);\n                    if (curRouteMatch) {\n                        Object.assign(query, curRouteMatch);\n                    }\n                }\n            }\n            // If the routeInfo brings a redirect we simply apply it.\n            if (\"type\" in routeInfo) {\n                if (routeInfo.type === \"redirect-internal\") {\n                    return this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                } else {\n                    handleHardNavigation({\n                        url: routeInfo.destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n            }\n            const component = routeInfo.Component;\n            if (component && component.unstable_scriptLoader) {\n                const scripts = [].concat(component.unstable_scriptLoader());\n                scripts.forEach((script)=>{\n                    (0, _script.handleClientScriptLoad)(script.props);\n                });\n            }\n            // handle redirect on client-transition\n            if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n                if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                    // Use the destination from redirect without adding locale\n                    options.locale = false;\n                    const destination = routeInfo.props.pageProps.__N_REDIRECT;\n                    // check if destination is internal (resolves to a page) and attempt\n                    // client-navigation if it is falling back to hard navigation if\n                    // it's not\n                    if (destination.startsWith(\"/\") && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                        const parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n                        parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                        const { url: newUrl, as: newAs } = prepareUrlAs(this, destination, destination);\n                        return this.change(method, newUrl, newAs, options);\n                    }\n                    handleHardNavigation({\n                        url: destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                // handle SSG data 404\n                if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n                    let notFoundRoute;\n                    try {\n                        await this.fetchComponent(\"/404\");\n                        notFoundRoute = \"/404\";\n                    } catch (_) {\n                        notFoundRoute = \"/_error\";\n                    }\n                    routeInfo = await this.getRouteInfo({\n                        route: notFoundRoute,\n                        pathname: notFoundRoute,\n                        query,\n                        as,\n                        resolvedAs,\n                        routeProps: {\n                            shallow: false\n                        },\n                        locale: nextState.locale,\n                        isPreview: nextState.isPreview,\n                        isNotFound: true\n                    });\n                    if (\"type\" in routeInfo) {\n                        throw new Error(\"Unexpected middleware effect on /404\");\n                    }\n                }\n            }\n            if (isQueryUpdating && this.pathname === \"/_error\" && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n                // ensure statusCode is still correct for static 500 page\n                // when updating query information\n                routeInfo.props.pageProps.statusCode = 500;\n            }\n            var _routeInfo_route;\n            // shallow routing is only allowed for same page URL changes.\n            const isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n            var _options_scroll;\n            const shouldScroll = (_options_scroll = options.scroll) != null ? _options_scroll : !isQueryUpdating && !isValidShallowRoute;\n            const resetScroll = shouldScroll ? {\n                x: 0,\n                y: 0\n            } : null;\n            const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n            // the new state that the router gonna set\n            const upcomingRouterState = {\n                ...nextState,\n                route,\n                pathname,\n                query,\n                asPath: cleanedAs,\n                isFallback: false\n            };\n            // When the page being rendered is the 404 page, we should only update the\n            // query parameters. Route changes here might add the basePath when it\n            // wasn't originally present. This is also why this block is before the\n            // below `changeState` call which updates the browser's history (changing\n            // the URL).\n            if (isQueryUpdating && isErrorRoute) {\n                var _self___NEXT_DATA___props_pageProps1, _self___NEXT_DATA___props1, _routeInfo_props1;\n                routeInfo = await this.getRouteInfo({\n                    route: this.pathname,\n                    pathname: this.pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps: {\n                        shallow: false\n                    },\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    isQueryUpdating: isQueryUpdating && !this.isFallback\n                });\n                if (\"type\" in routeInfo) {\n                    throw new Error(\"Unexpected middleware effect on \" + this.pathname);\n                }\n                if (this.pathname === \"/_error\" && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    routeInfo.props.pageProps.statusCode = 500;\n                }\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (err) {\n                    if ((0, _iserror.default)(err) && err.cancelled) {\n                        Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                return true;\n            }\n            Router.events.emit(\"beforeHistoryChange\", as, routeProps);\n            this.changeState(method, url, as, options);\n            // for query updates we can skip it if the state is unchanged and we don't\n            // need to scroll\n            // https://github.com/vercel/next.js/issues/37139\n            const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, this.state);\n            if (!canSkipUpdating) {\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (e) {\n                    if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                    else throw e;\n                }\n                if (routeInfo.error) {\n                    if (!isQueryUpdating) {\n                        Router.events.emit(\"routeChangeError\", routeInfo.error, cleanedAs, routeProps);\n                    }\n                    throw routeInfo.error;\n                }\n                if (false) {}\n                if (!isQueryUpdating) {\n                    Router.events.emit(\"routeChangeComplete\", as, routeProps);\n                }\n                // A hash mark # is the optional last part of a URL\n                const hashRegex = /#.+$/;\n                if (shouldScroll && hashRegex.test(as)) {\n                    this.scrollToHash(as);\n                }\n            }\n            return true;\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.cancelled) {\n                return false;\n            }\n            throw err;\n        }\n    }\n    changeState(method, url, as, options) {\n        if (options === void 0) options = {};\n        if (true) {\n            if (typeof window.history === \"undefined\") {\n                console.error(\"Warning: window.history is not available.\");\n                return;\n            }\n            if (typeof window.history[method] === \"undefined\") {\n                console.error(\"Warning: window.history.\" + method + \" is not available\");\n                return;\n            }\n        }\n        if (method !== \"pushState\" || (0, _utils.getURL)() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== \"pushState\" ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/docs/Web/API/History/replaceState\n            \"\", as);\n        }\n    }\n    async handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        console.error(err);\n        if (err.cancelled) {\n            // bubble up cancellation errors\n            throw err;\n        }\n        if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n            Router.events.emit(\"routeChangeError\", err, as, routeProps);\n            // If we can't load the page it could be one of following reasons\n            //  1. Page doesn't exists\n            //  2. Page does exist in a different zone\n            //  3. Internal error while loading the page\n            // So, doing a hard reload is the proper way to deal with this.\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            // Changing the URL doesn't block executing the current code path.\n            // So let's throw a cancellation error stop the routing logic.\n            throw buildCancellationError();\n        }\n        try {\n            let props;\n            const { page: Component, styleSheets } = await this.fetchComponent(\"/_error\");\n            const routeInfo = {\n                props,\n                Component,\n                styleSheets,\n                err,\n                error: err\n            };\n            if (!routeInfo.props) {\n                try {\n                    routeInfo.props = await this.getInitialProps(Component, {\n                        err,\n                        pathname,\n                        query\n                    });\n                } catch (gipErr) {\n                    console.error(\"Error in error page `getInitialProps`: \", gipErr);\n                    routeInfo.props = {};\n                }\n            }\n            return routeInfo;\n        } catch (routeInfoErr) {\n            return this.handleRouteInfoError((0, _iserror.default)(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + \"\"), pathname, query, as, routeProps, true);\n        }\n    }\n    async getRouteInfo(param) {\n        let { route: requestedRoute, pathname, query, as, resolvedAs, routeProps, locale, hasMiddleware, isPreview, unstable_skipClientCache, isQueryUpdating, isMiddlewareRewrite, isNotFound } = param;\n        /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n        try {\n            var _data_effect, _data_effect1, _data_effect2, _data_response;\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: this\n            });\n            let existingInfo = this.components[route];\n            if (routeProps.shallow && existingInfo && this.route === route) {\n                return existingInfo;\n            }\n            if (hasMiddleware) {\n                existingInfo = undefined;\n            }\n            let cachedRouteInfo = existingInfo && !(\"initial\" in existingInfo) && \"development\" !== \"development\" ? 0 : undefined;\n            const isBackground = isQueryUpdating;\n            const fetchNextDataParams = {\n                dataHref: this.pageLoader.getDataHref({\n                    href: (0, _formaturl.formatWithValidation)({\n                        pathname,\n                        query\n                    }),\n                    skipInterpolation: true,\n                    asPath: isNotFound ? \"/404\" : resolvedAs,\n                    locale\n                }),\n                hasMiddleware: true,\n                isServerRender: this.isSsr,\n                parseJSON: true,\n                inflightCache: isBackground ? this.sbc : this.sdc,\n                persistCache: !isPreview,\n                isPrefetch: false,\n                unstable_skipClientCache,\n                isBackground\n            };\n            let data = isQueryUpdating && !isMiddlewareRewrite ? null : await withMiddlewareEffects({\n                fetchData: ()=>fetchNextData(fetchNextDataParams),\n                asPath: isNotFound ? \"/404\" : resolvedAs,\n                locale: locale,\n                router: this\n            }).catch((err)=>{\n                // we don't hard error during query updating\n                // as it's un-necessary and doesn't need to be fatal\n                // unless it is a fallback route and the props can't\n                // be loaded\n                if (isQueryUpdating) {\n                    return null;\n                }\n                throw err;\n            });\n            // when rendering error routes we don't apply middleware\n            // effects\n            if (data && (pathname === \"/_error\" || pathname === \"/404\")) {\n                data.effect = undefined;\n            }\n            if (isQueryUpdating) {\n                if (!data) {\n                    data = {\n                        json: self.__NEXT_DATA__.props\n                    };\n                } else {\n                    data.json = self.__NEXT_DATA__.props;\n                }\n            }\n            handleCancelled();\n            if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === \"redirect-internal\" || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === \"redirect-external\") {\n                return data.effect;\n            }\n            if ((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === \"rewrite\") {\n                const resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n                const pages = await this.pageLoader.getPageList();\n                // during query updating the page must match although during\n                // client-transition a redirect that doesn't match a page\n                // can be returned and this should trigger a hard navigation\n                // which is valid for incremental migration\n                if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                    route = resolvedRoute;\n                    pathname = data.effect.resolvedHref;\n                    query = {\n                        ...query,\n                        ...data.effect.parsedAs.query\n                    };\n                    resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, this.locales).pathname);\n                    // Check again the cache with the new destination.\n                    existingInfo = this.components[route];\n                    if (routeProps.shallow && existingInfo && this.route === route && !hasMiddleware) {\n                        // If we have a match with the current route due to rewrite,\n                        // we can copy the existing information to the rewritten one.\n                        // Then, we return the information along with the matched route.\n                        return {\n                            ...existingInfo,\n                            route\n                        };\n                    }\n                }\n            }\n            if ((0, _isapiroute.isAPIRoute)(route)) {\n                handleHardNavigation({\n                    url: as,\n                    router: this\n                });\n                return new Promise(()=>{});\n            }\n            const routeInfo = cachedRouteInfo || await this.fetchComponent(route).then((res)=>({\n                    Component: res.page,\n                    styleSheets: res.styleSheets,\n                    __N_SSG: res.mod.__N_SSG,\n                    __N_SSP: res.mod.__N_SSP\n                }));\n            if (true) {\n                const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"./node_modules/next/dist/compiled/react-is/index.js\");\n                if (!isValidElementType(routeInfo.Component)) {\n                    throw new Error('The default export is not a React Component in page: \"' + pathname + '\"');\n                }\n            }\n            const wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get(\"x-middleware-skip\");\n            const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n            // For non-SSG prefetches that bailed before sending data\n            // we clear the cache to fetch full response\n            if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                delete this.sdc[data.dataHref];\n            }\n            const { props, cacheKey } = await this._getData(async ()=>{\n                if (shouldFetchData) {\n                    if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                        return {\n                            cacheKey: data.cacheKey,\n                            props: data.json\n                        };\n                    }\n                    const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname,\n                            query\n                        }),\n                        asPath: resolvedAs,\n                        locale\n                    });\n                    const fetched = await fetchNextData({\n                        dataHref,\n                        isServerRender: this.isSsr,\n                        parseJSON: true,\n                        inflightCache: wasBailedPrefetch ? {} : this.sdc,\n                        persistCache: !isPreview,\n                        isPrefetch: false,\n                        unstable_skipClientCache\n                    });\n                    return {\n                        cacheKey: fetched.cacheKey,\n                        props: fetched.json || {}\n                    };\n                }\n                return {\n                    headers: {},\n                    props: await this.getInitialProps(routeInfo.Component, {\n                        pathname,\n                        query,\n                        asPath: as,\n                        locale,\n                        locales: this.locales,\n                        defaultLocale: this.defaultLocale\n                    })\n                };\n            });\n            // Only bust the data cache for SSP routes although\n            // middleware can skip cache per request with\n            // x-middleware-cache: no-cache as well\n            if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                delete this.sdc[cacheKey];\n            }\n            // we kick off a HEAD request in the background\n            // when a non-prefetch request is made to signal revalidation\n            if (!this.isPreview && routeInfo.__N_SSG && \"development\" !== \"development\" && 0) {}\n            props.pageProps = Object.assign({}, props.pageProps);\n            routeInfo.props = props;\n            routeInfo.route = route;\n            routeInfo.query = query;\n            routeInfo.resolvedAs = resolvedAs;\n            this.components[route] = routeInfo;\n            return routeInfo;\n        } catch (err) {\n            return this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps);\n        }\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components[\"/_app\"].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split(\"#\", 2);\n        const [newUrlNoHash, newHash] = as.split(\"#\", 2);\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = \"\"] = as.split(\"#\", 2);\n        (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n            // Scroll to top if the hash is just `#` with no value or `#top`\n            // To mirror browsers\n            if (hash === \"\" || hash === \"top\") {\n                window.scrollTo(0, 0);\n                return;\n            }\n            // Decode hash to make non-latin anchor works.\n            const rawHash = decodeURIComponent(hash);\n            // First we check if the element by id is found\n            const idEl = document.getElementById(rawHash);\n            if (idEl) {\n                idEl.scrollIntoView();\n                return;\n            }\n            // If there's no element with the id, we check the `name` property\n            // To mirror browsers\n            const nameEl = document.getElementsByName(rawHash)[0];\n            if (nameEl) {\n                nameEl.scrollIntoView();\n            }\n        }, {\n            onlyHashChange: this.onlyAHashChange(as)\n        });\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ async prefetch(url, asPath, options) {\n        if (asPath === void 0) asPath = url;\n        if (options === void 0) options = {};\n        // Prefetch is not supported in development mode because it would trigger on-demand-entries\n        if (true) {\n            return;\n        }\n        if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n            // No prefetches for bots that render the link since they are typically navigating\n            // links via the equivalent of a hard navigation and hence never utilize these\n            // prefetches.\n            return;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        const urlPathname = parsed.pathname;\n        let { pathname, query } = parsed;\n        const originalPathname = pathname;\n        if (false) {}\n        const pages = await this.pageLoader.getPageList();\n        let resolvedAs = asPath;\n        const locale = typeof options.locale !== \"undefined\" ? options.locale || undefined : this.locale;\n        const isMiddlewareMatch = await matchesMiddleware({\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        if (false) {}\n        parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n        if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n            pathname = parsed.pathname;\n            parsed.pathname = pathname;\n            Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n            if (!isMiddlewareMatch) {\n                url = (0, _formaturl.formatWithValidation)(parsed);\n            }\n        }\n        const data =  false ? 0 : await withMiddlewareEffects({\n            fetchData: ()=>fetchNextData({\n                    dataHref: this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname: originalPathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: this.isSsr,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true\n                }),\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === \"rewrite\") {\n            parsed.pathname = data.effect.resolvedHref;\n            pathname = data.effect.resolvedHref;\n            query = {\n                ...query,\n                ...data.effect.parsedAs.query\n            };\n            resolvedAs = data.effect.parsedAs.pathname;\n            url = (0, _formaturl.formatWithValidation)(parsed);\n        }\n        /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === \"redirect-external\") {\n            return;\n        }\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n            this.components[urlPathname] = {\n                __appRouter: true\n            };\n        }\n        await Promise.all([\n            this.pageLoader._isSsg(route).then((isSsg)=>{\n                return isSsg ? fetchNextData({\n                    dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : this.pageLoader.getDataHref({\n                        href: url,\n                        asPath: resolvedAs,\n                        locale: locale\n                    }),\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true,\n                    unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                }).then(()=>false).catch(()=>false) : false;\n            }),\n            this.pageLoader[options.priority ? \"loadPage\" : \"prefetch\"](route)\n        ]);\n    }\n    async fetchComponent(route) {\n        const handleCancelled = getCancelledHandler({\n            route,\n            router: this\n        });\n        try {\n            const componentResult = await this.pageLoader.loadPage(route);\n            handleCancelled();\n            return componentResult;\n        } catch (err) {\n            handleCancelled();\n            throw err;\n        }\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = new Error(\"Loading initial props cancelled\");\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    _getFlightData(dataHref) {\n        // Do not cache RSC flight response since it's not a static resource\n        return fetchNextData({\n            dataHref,\n            isServerRender: true,\n            parseJSON: false,\n            inflightCache: this.sdc,\n            persistCache: false,\n            isPrefetch: false\n        }).then((param)=>{\n            let { text } = param;\n            return {\n                data: text\n            };\n        });\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App } = this.components[\"/_app\"];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils.loadGetInitialProps)(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname, query, as, { initialProps, pageLoader, App, wrapApp, Component, err, subscription, isFallback, locale, locales, defaultLocale, domainLocales, isPreview }){\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname, query } = this;\n                this.changeState(\"replaceState\", (0, _formaturl.formatWithValidation)({\n                    pathname: (0, _addbasepath.addBasePath)(pathname),\n                    query\n                }), (0, _utils.getURL)());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url, as, options, key } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname } = (0, _parserelativeurl.parseRelativeUrl)(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addbasepath.addBasePath)(this.asPath) && pathname === (0, _addbasepath.addBasePath)(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change(\"replaceState\", url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname !== \"/_error\") {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components[\"/_app\"] = {\n            Component: App,\n            styleSheets: []\n        };\n        if (true) {\n            const { BloomFilter } = __webpack_require__(/*! ../../lib/bloom-filter */ \"./node_modules/next/dist/shared/lib/bloom-filter.js\");\n            const staticFilterData = {\"numItems\":20,\"errorRate\":0.01,\"numBits\":192,\"numHashes\":7,\"bitArray\":[0,1,0,1,0,1,1,1,1,0,0,1,1,1,1,1,0,1,1,1,1,0,1,0,1,1,0,1,1,0,0,0,1,1,1,0,0,0,1,1,0,0,1,0,1,1,0,1,0,0,0,0,1,0,1,1,0,0,1,0,0,0,0,1,0,0,1,1,0,1,1,0,1,0,0,0,1,1,1,1,0,0,0,1,1,0,1,1,0,0,0,1,1,1,0,1,0,0,1,1,0,0,0,0,1,1,1,1,0,0,1,1,1,1,1,1,1,0,0,1,0,1,1,1,1,0,0,1,1,0,0,1,1,0,0,0,1,0,1,1,0,0,0,1,0,0,0,1,1,1,0,0,0,1,1,1,1,1,1,0,1,1,1,0,1,0,1,0,1,1,1,1,1,1,0,1,0,1,1,0,0,0,0,0,1,0,1,1,0,1,0,1]};\n            const dynamicFilterData = {\"numItems\":0,\"errorRate\":0.01,\"numBits\":0,\"numHashes\":null,\"bitArray\":[]};\n            if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n                this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n                this._bfl_s.import(staticFilterData);\n            }\n            if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n                this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n                this._bfl_d.import(dynamicFilterData);\n            }\n        }\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || \"\";\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.isExperimentalCompile || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n        if (false) {}\n        this.state = {\n            route,\n            pathname,\n            query,\n            asPath: autoExportDynamic ? pathname : as,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as.startsWith(\"//\")) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                const options = {\n                    locale\n                };\n                const asPath = (0, _utils.getURL)();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale,\n                    asPath\n                }).then((matches)=>{\n                    options._shouldResolveHref = as !== pathname;\n                    this.changeState(\"replaceState\", matches ? asPath : (0, _formaturl.formatWithValidation)({\n                        pathname: (0, _addbasepath.addBasePath)(pathname),\n                        query\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener(\"popstate\", this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n}\nRouter.events = (0, _mitt.default)(); //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});