[{"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\bolts\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\hex-bolts\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\nuts\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\screws\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\shear-welding-studs\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\structure-bolts\\page.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\studs\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\washers\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\bolt-preload\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\coarse-vs-fine-threads\\page.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\european-surface-treatment\\page.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\failure-analysis\\page.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\hardness-conversion\\page.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\page.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\test-i18n\\page.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\test-translation\\page.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\contact\\ContactForm.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\contact\\ContactInfo.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\AboutSection.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\CorporateCultureSection.tsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\HeroSection.tsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\ProductsSection.tsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\TechnicalSection.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\layout\\Footer.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\layout\\Header.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\layout\\LanguageSwitcher.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\products\\ProductCategories.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\products\\ProductDetail.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\products\\ProductGrid.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\technical\\TechnicalArticles.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\contexts\\LanguageContext.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\data\\products.ts": "37"}, {"size": 6314, "mtime": 1755765949444, "results": "38", "hashOfConfig": "39"}, {"size": 1240, "mtime": 1756365597213, "results": "40", "hashOfConfig": "39"}, {"size": 1524, "mtime": 1756028563905, "results": "41", "hashOfConfig": "39"}, {"size": 685, "mtime": 1755765704135, "results": "42", "hashOfConfig": "39"}, {"size": 3526, "mtime": 1756367121496, "results": "43", "hashOfConfig": "39"}, {"size": 4099, "mtime": 1756088798143, "results": "44", "hashOfConfig": "39"}, {"size": 2733, "mtime": 1756366683217, "results": "45", "hashOfConfig": "39"}, {"size": 1007, "mtime": 1755765958752, "results": "46", "hashOfConfig": "39"}, {"size": 2915, "mtime": 1756367318791, "results": "47", "hashOfConfig": "39"}, {"size": 4148, "mtime": 1756088781494, "results": "48", "hashOfConfig": "39"}, {"size": 4338, "mtime": 1756088744383, "results": "49", "hashOfConfig": "39"}, {"size": 2757, "mtime": 1756367529941, "results": "50", "hashOfConfig": "39"}, {"size": 2684, "mtime": 1756367195475, "results": "51", "hashOfConfig": "39"}, {"size": 9992, "mtime": 1756028052403, "results": "52", "hashOfConfig": "39"}, {"size": 5954, "mtime": 1756027861753, "results": "53", "hashOfConfig": "39"}, {"size": 9282, "mtime": 1756027911584, "results": "54", "hashOfConfig": "39"}, {"size": 8810, "mtime": 1756028104855, "results": "55", "hashOfConfig": "39"}, {"size": 9984, "mtime": 1756027955257, "results": "56", "hashOfConfig": "39"}, {"size": 955, "mtime": 1756365460028, "results": "57", "hashOfConfig": "39"}, {"size": 5395, "mtime": 1756028725285, "results": "58", "hashOfConfig": "39"}, {"size": 3722, "mtime": 1756030068220, "results": "59", "hashOfConfig": "39"}, {"size": 6961, "mtime": 1756365770948, "results": "60", "hashOfConfig": "39"}, {"size": 3791, "mtime": 1756365669694, "results": "61", "hashOfConfig": "39"}, {"size": 3765, "mtime": 1756029313042, "results": "62", "hashOfConfig": "39"}, {"size": 4066, "mtime": 1756029435901, "results": "63", "hashOfConfig": "39"}, {"size": 4507, "mtime": 1756029120529, "results": "64", "hashOfConfig": "39"}, {"size": 4451, "mtime": 1756085456548, "results": "65", "hashOfConfig": "39"}, {"size": 4509, "mtime": 1756029541599, "results": "66", "hashOfConfig": "39"}, {"size": 5508, "mtime": 1756365123255, "results": "67", "hashOfConfig": "39"}, {"size": 7150, "mtime": 1756366474102, "results": "68", "hashOfConfig": "39"}, {"size": 2072, "mtime": 1756028594700, "results": "69", "hashOfConfig": "39"}, {"size": 3844, "mtime": 1755765979678, "results": "70", "hashOfConfig": "39"}, {"size": 7628, "mtime": 1756089100132, "results": "71", "hashOfConfig": "39"}, {"size": 5155, "mtime": 1755766011612, "results": "72", "hashOfConfig": "39"}, {"size": 8590, "mtime": 1756365578969, "results": "73", "hashOfConfig": "39"}, {"size": 3202, "mtime": 1756088243102, "results": "74", "hashOfConfig": "39"}, {"size": 5186, "mtime": 1756088513295, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1a1or5d", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\about\\page.tsx", ["187", "188", "189", "190"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\bolts\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\hex-bolts\\page.tsx", ["191"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\nuts\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\screws\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\shear-welding-studs\\page.tsx", ["192"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\structure-bolts\\page.tsx", ["193"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\studs\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\washers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\bolt-preload\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\coarse-vs-fine-threads\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\european-surface-treatment\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\failure-analysis\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\hardness-conversion\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\technical\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\test-i18n\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\test-translation\\page.tsx", ["194", "195", "196", "197", "198", "199"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\contact\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\contact\\ContactInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\AboutSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\CorporateCultureSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\HeroSection.tsx", ["200"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\ProductsSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\TechnicalSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\layout\\LanguageSwitcher.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\products\\ProductCategories.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\products\\ProductDetail.tsx", ["201", "202"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\products\\ProductGrid.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\technical\\TechnicalArticles.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\contexts\\LanguageContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\data\\products.ts", [], [], {"ruleId": "203", "severity": 2, "message": "204", "line": 62, "column": 23, "nodeType": "205", "messageId": "206", "suggestions": "207"}, {"ruleId": "203", "severity": 2, "message": "204", "line": 62, "column": 31, "nodeType": "205", "messageId": "206", "suggestions": "208"}, {"ruleId": "203", "severity": 2, "message": "204", "line": 72, "column": 39, "nodeType": "205", "messageId": "206", "suggestions": "209"}, {"ruleId": "203", "severity": 2, "message": "204", "line": 72, "column": 49, "nodeType": "205", "messageId": "206", "suggestions": "210"}, {"ruleId": "211", "severity": 1, "message": "212", "line": 82, "column": 21, "nodeType": "213", "endLine": 86, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "212", "line": 82, "column": 21, "nodeType": "213", "endLine": 86, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "212", "line": 86, "column": 21, "nodeType": "213", "endLine": 90, "endColumn": 23}, {"ruleId": "203", "severity": 2, "message": "214", "line": 74, "column": 21, "nodeType": "205", "messageId": "206", "suggestions": "215"}, {"ruleId": "203", "severity": 2, "message": "214", "line": 74, "column": 30, "nodeType": "205", "messageId": "206", "suggestions": "216"}, {"ruleId": "203", "severity": 2, "message": "214", "line": 77, "column": 21, "nodeType": "205", "messageId": "206", "suggestions": "217"}, {"ruleId": "203", "severity": 2, "message": "214", "line": 77, "column": 33, "nodeType": "205", "messageId": "206", "suggestions": "218"}, {"ruleId": "203", "severity": 2, "message": "214", "line": 80, "column": 21, "nodeType": "205", "messageId": "206", "suggestions": "219"}, {"ruleId": "203", "severity": 2, "message": "214", "line": 80, "column": 39, "nodeType": "205", "messageId": "206", "suggestions": "220"}, {"ruleId": "221", "severity": 1, "message": "222", "line": 45, "column": 6, "nodeType": "223", "endLine": 45, "endColumn": 8, "suggestions": "224"}, {"ruleId": "211", "severity": 1, "message": "212", "line": 58, "column": 13, "nodeType": "213", "endLine": 62, "endColumn": 15}, {"ruleId": "211", "severity": 1, "message": "212", "line": 101, "column": 17, "nodeType": "213", "endLine": 105, "endColumn": 19}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["225", "226", "227", "228"], ["229", "230", "231", "232"], ["233", "234", "235", "236"], ["237", "238", "239", "240"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", ["241", "242", "243", "244"], ["245", "246", "247", "248"], ["249", "250", "251", "252"], ["253", "254", "255", "256"], ["257", "258", "259", "260"], ["261", "262", "263", "264"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'slides.length'. Either include it or remove the dependency array.", "ArrayExpression", ["265"], {"messageId": "266", "data": "267", "fix": "268", "desc": "269"}, {"messageId": "266", "data": "270", "fix": "271", "desc": "272"}, {"messageId": "266", "data": "273", "fix": "274", "desc": "275"}, {"messageId": "266", "data": "276", "fix": "277", "desc": "278"}, {"messageId": "266", "data": "279", "fix": "280", "desc": "269"}, {"messageId": "266", "data": "281", "fix": "282", "desc": "272"}, {"messageId": "266", "data": "283", "fix": "284", "desc": "275"}, {"messageId": "266", "data": "285", "fix": "286", "desc": "278"}, {"messageId": "266", "data": "287", "fix": "288", "desc": "269"}, {"messageId": "266", "data": "289", "fix": "290", "desc": "272"}, {"messageId": "266", "data": "291", "fix": "292", "desc": "275"}, {"messageId": "266", "data": "293", "fix": "294", "desc": "278"}, {"messageId": "266", "data": "295", "fix": "296", "desc": "269"}, {"messageId": "266", "data": "297", "fix": "298", "desc": "272"}, {"messageId": "266", "data": "299", "fix": "300", "desc": "275"}, {"messageId": "266", "data": "301", "fix": "302", "desc": "278"}, {"messageId": "266", "data": "303", "fix": "304", "desc": "305"}, {"messageId": "266", "data": "306", "fix": "307", "desc": "308"}, {"messageId": "266", "data": "309", "fix": "310", "desc": "311"}, {"messageId": "266", "data": "312", "fix": "313", "desc": "314"}, {"messageId": "266", "data": "315", "fix": "316", "desc": "305"}, {"messageId": "266", "data": "317", "fix": "318", "desc": "308"}, {"messageId": "266", "data": "319", "fix": "320", "desc": "311"}, {"messageId": "266", "data": "321", "fix": "322", "desc": "314"}, {"messageId": "266", "data": "323", "fix": "324", "desc": "305"}, {"messageId": "266", "data": "325", "fix": "326", "desc": "308"}, {"messageId": "266", "data": "327", "fix": "328", "desc": "311"}, {"messageId": "266", "data": "329", "fix": "330", "desc": "314"}, {"messageId": "266", "data": "331", "fix": "332", "desc": "305"}, {"messageId": "266", "data": "333", "fix": "334", "desc": "308"}, {"messageId": "266", "data": "335", "fix": "336", "desc": "311"}, {"messageId": "266", "data": "337", "fix": "338", "desc": "314"}, {"messageId": "266", "data": "339", "fix": "340", "desc": "305"}, {"messageId": "266", "data": "341", "fix": "342", "desc": "308"}, {"messageId": "266", "data": "343", "fix": "344", "desc": "311"}, {"messageId": "266", "data": "345", "fix": "346", "desc": "314"}, {"messageId": "266", "data": "347", "fix": "348", "desc": "305"}, {"messageId": "266", "data": "349", "fix": "350", "desc": "308"}, {"messageId": "266", "data": "351", "fix": "352", "desc": "311"}, {"messageId": "266", "data": "353", "fix": "354", "desc": "314"}, {"desc": "355", "fix": "356"}, "replaceWithAlt", {"alt": "357"}, {"range": "358", "text": "359"}, "Replace with `&quot;`.", {"alt": "360"}, {"range": "361", "text": "362"}, "Replace with `&ldquo;`.", {"alt": "363"}, {"range": "364", "text": "365"}, "Replace with `&#34;`.", {"alt": "366"}, {"range": "367", "text": "368"}, "Replace with `&rdquo;`.", {"alt": "357"}, {"range": "369", "text": "370"}, {"alt": "360"}, {"range": "371", "text": "372"}, {"alt": "363"}, {"range": "373", "text": "374"}, {"alt": "366"}, {"range": "375", "text": "376"}, {"alt": "357"}, {"range": "377", "text": "378"}, {"alt": "360"}, {"range": "379", "text": "380"}, {"alt": "363"}, {"range": "381", "text": "382"}, {"alt": "366"}, {"range": "383", "text": "384"}, {"alt": "357"}, {"range": "385", "text": "386"}, {"alt": "360"}, {"range": "387", "text": "388"}, {"alt": "363"}, {"range": "389", "text": "390"}, {"alt": "366"}, {"range": "391", "text": "392"}, {"alt": "393"}, {"range": "394", "text": "395"}, "Replace with `&apos;`.", {"alt": "396"}, {"range": "397", "text": "398"}, "Replace with `&lsquo;`.", {"alt": "399"}, {"range": "400", "text": "401"}, "Replace with `&#39;`.", {"alt": "402"}, {"range": "403", "text": "404"}, "Replace with `&rsquo;`.", {"alt": "393"}, {"range": "405", "text": "406"}, {"alt": "396"}, {"range": "407", "text": "408"}, {"alt": "399"}, {"range": "409", "text": "410"}, {"alt": "402"}, {"range": "411", "text": "412"}, {"alt": "393"}, {"range": "413", "text": "414"}, {"alt": "396"}, {"range": "415", "text": "416"}, {"alt": "399"}, {"range": "417", "text": "418"}, {"alt": "402"}, {"range": "419", "text": "420"}, {"alt": "393"}, {"range": "421", "text": "422"}, {"alt": "396"}, {"range": "423", "text": "424"}, {"alt": "399"}, {"range": "425", "text": "426"}, {"alt": "402"}, {"range": "427", "text": "428"}, {"alt": "393"}, {"range": "429", "text": "430"}, {"alt": "396"}, {"range": "431", "text": "432"}, {"alt": "399"}, {"range": "433", "text": "434"}, {"alt": "402"}, {"range": "435", "text": "436"}, {"alt": "393"}, {"range": "437", "text": "438"}, {"alt": "396"}, {"range": "439", "text": "440"}, {"alt": "399"}, {"range": "441", "text": "442"}, {"alt": "402"}, {"range": "443", "text": "444"}, "Update the dependencies array to be: [slides.length]", {"range": "445", "text": "446"}, "&quot;", [1950, 2124], "\n                  联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。\n                  公司位于&quot;中国紧固件之都\"——河北省邯郸市永年区，占地面积7000平方米，\n                  包括冷锻车间、热锻车间、热处理车间、仓库和实验室。\n                ", "&ldquo;", [1950, 2124], "\n                  联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。\n                  公司位于&ldquo;中国紧固件之都\"——河北省邯郸市永年区，占地面积7000平方米，\n                  包括冷锻车间、热锻车间、热处理车间、仓库和实验室。\n                ", "&#34;", [1950, 2124], "\n                  联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。\n                  公司位于&#34;中国紧固件之都\"——河北省邯郸市永年区，占地面积7000平方米，\n                  包括冷锻车间、热锻车间、热处理车间、仓库和实验室。\n                ", "&rdquo;", [1950, 2124], "\n                  联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。\n                  公司位于&rdquo;中国紧固件之都\"——河北省邯郸市永年区，占地面积7000平方米，\n                  包括冷锻车间、热锻车间、热处理车间、仓库和实验室。\n                ", [1950, 2124], "\n                  联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。\n                  公司位于\"中国紧固件之都&quot;——河北省邯郸市永年区，占地面积7000平方米，\n                  包括冷锻车间、热锻车间、热处理车间、仓库和实验室。\n                ", [1950, 2124], "\n                  联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。\n                  公司位于\"中国紧固件之都&ldquo;——河北省邯郸市永年区，占地面积7000平方米，\n                  包括冷锻车间、热锻车间、热处理车间、仓库和实验室。\n                ", [1950, 2124], "\n                  联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。\n                  公司位于\"中国紧固件之都&#34;——河北省邯郸市永年区，占地面积7000平方米，\n                  包括冷锻车间、热锻车间、热处理车间、仓库和实验室。\n                ", [1950, 2124], "\n                  联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。\n                  公司位于\"中国紧固件之都&rdquo;——河北省邯郸市永年区，占地面积7000平方米，\n                  包括冷锻车间、热锻车间、热处理车间、仓库和实验室。\n                ", [2329, 2481], "\n                  公司产品广泛应用于建筑、机械、汽车、电力、石化等行业，\n                  深受国内外客户的信赖和好评。我们始终坚持&quot;质量第一、客户至上\"的经营理念，\n                  不断提升产品质量和服务水平。\n                ", [2329, 2481], "\n                  公司产品广泛应用于建筑、机械、汽车、电力、石化等行业，\n                  深受国内外客户的信赖和好评。我们始终坚持&ldquo;质量第一、客户至上\"的经营理念，\n                  不断提升产品质量和服务水平。\n                ", [2329, 2481], "\n                  公司产品广泛应用于建筑、机械、汽车、电力、石化等行业，\n                  深受国内外客户的信赖和好评。我们始终坚持&#34;质量第一、客户至上\"的经营理念，\n                  不断提升产品质量和服务水平。\n                ", [2329, 2481], "\n                  公司产品广泛应用于建筑、机械、汽车、电力、石化等行业，\n                  深受国内外客户的信赖和好评。我们始终坚持&rdquo;质量第一、客户至上\"的经营理念，\n                  不断提升产品质量和服务水平。\n                ", [2329, 2481], "\n                  公司产品广泛应用于建筑、机械、汽车、电力、石化等行业，\n                  深受国内外客户的信赖和好评。我们始终坚持\"质量第一、客户至上&quot;的经营理念，\n                  不断提升产品质量和服务水平。\n                ", [2329, 2481], "\n                  公司产品广泛应用于建筑、机械、汽车、电力、石化等行业，\n                  深受国内外客户的信赖和好评。我们始终坚持\"质量第一、客户至上&ldquo;的经营理念，\n                  不断提升产品质量和服务水平。\n                ", [2329, 2481], "\n                  公司产品广泛应用于建筑、机械、汽车、电力、石化等行业，\n                  深受国内外客户的信赖和好评。我们始终坚持\"质量第一、客户至上&#34;的经营理念，\n                  不断提升产品质量和服务水平。\n                ", [2329, 2481], "\n                  公司产品广泛应用于建筑、机械、汽车、电力、石化等行业，\n                  深受国内外客户的信赖和好评。我们始终坚持\"质量第一、客户至上&rdquo;的经营理念，\n                  不断提升产品质量和服务水平。\n                ", "&apos;", [2666, 2700], "\n                  t(&apos;nav.home'): ", "&lsquo;", [2666, 2700], "\n                  t(&lsquo;nav.home'): ", "&#39;", [2666, 2700], "\n                  t(&#39;nav.home'): ", "&rsquo;", [2666, 2700], "\n                  t(&rsquo;nav.home'): ", [2666, 2700], "\n                  t('nav.home&apos;): ", [2666, 2700], "\n                  t('nav.home&lsquo;): ", [2666, 2700], "\n                  t('nav.home&#39;): ", [2666, 2700], "\n                  t('nav.home&rsquo;): ", [2806, 2843], "\n                  t(&apos;hero.title1'): ", [2806, 2843], "\n                  t(&lsquo;hero.title1'): ", [2806, 2843], "\n                  t(&#39;hero.title1'): ", [2806, 2843], "\n                  t(&rsquo;hero.title1'): ", [2806, 2843], "\n                  t('hero.title1&apos;): ", [2806, 2843], "\n                  t('hero.title1&lsquo;): ", [2806, 2843], "\n                  t('hero.title1&#39;): ", [2806, 2843], "\n                  t('hero.title1&rsquo;): ", [2952, 2995], "\n                  t(&apos;hero.description1'): ", [2952, 2995], "\n                  t(&lsquo;hero.description1'): ", [2952, 2995], "\n                  t(&#39;hero.description1'): ", [2952, 2995], "\n                  t(&rsquo;hero.description1'): ", [2952, 2995], "\n                  t('hero.description1&apos;): ", [2952, 2995], "\n                  t('hero.description1&lsquo;): ", [2952, 2995], "\n                  t('hero.description1&#39;): ", [2952, 2995], "\n                  t('hero.description1&rsquo;): ", [1119, 1121], "[slides.length]"]