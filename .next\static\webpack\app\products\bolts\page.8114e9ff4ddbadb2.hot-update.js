"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/bolts/page",{

/***/ "(app-pages-browser)/./src/app/products/bolts/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/products/bolts/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BoltsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_products_ProductDetail__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/products/ProductDetail */ \"(app-pages-browser)/./src/components/products/ProductDetail.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BoltsPage() {\n    _s();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const boltProducts = [\n        {\n            id: \"hex-bolt\",\n            slug: \"hex-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.hexBolt.name\",\n            descriptionKey: \"products.categories.bolts.hexBolt.description\",\n            shortDescKey: \"products.categories.bolts.hexBolt.description\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    valueKey: \"hexBolt.spec\"\n                },\n                {\n                    key: \"specifications.strength\",\n                    valueKey: \"hexBolt.strength\"\n                },\n                {\n                    key: \"specifications.material\",\n                    valueKey: \"hexBolt.material\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    valueKey: \"hexBolt.surface\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"applications.mechanical\"\n                },\n                {\n                    key: \"applications.construction\"\n                },\n                {\n                    key: \"applications.automotive\"\n                },\n                {\n                    key: \"applications.electrical\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"Hex Bolt\"\n                },\n                {\n                    url: \"https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n                    alt: \"Hex Bolt Detail\"\n                }\n            ],\n            features: []\n        },\n        {\n            id: \"flange-bolt\",\n            slug: \"flange-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.flangeBolt.name\",\n            descriptionKey: \"products.categories.bolts.flangeBolt.description\",\n            shortDescKey: \"products.categories.bolts.flangeBolt.description\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    valueKey: \"flangeBolt.spec\"\n                },\n                {\n                    key: \"specifications.strength\",\n                    valueKey: \"flangeBolt.strength\"\n                },\n                {\n                    key: \"specifications.material\",\n                    valueKey: \"flangeBolt.material\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    valueKey: \"flangeBolt.surface\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"applications.automotive\"\n                },\n                {\n                    key: \"applications.mechanical\"\n                },\n                {\n                    key: \"applications.precision\"\n                },\n                {\n                    key: \"applications.high-strength\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"Flange Bolt\"\n                }\n            ],\n            features: []\n        },\n        {\n            id: \"u-bolt\",\n            slug: \"u-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.bolts.uBolt.name\",\n            descriptionKey: \"products.bolts.uBolt.description\",\n            shortDescKey: \"products.bolts.uBolt.shortDesc\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    valueKey: \"uBolt.spec\"\n                },\n                {\n                    key: \"specifications.material\",\n                    valueKey: \"uBolt.material\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    valueKey: \"uBolt.surface\"\n                },\n                {\n                    key: \"specifications.shape\",\n                    valueKey: \"uBolt.shape\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"applications.pipe-fixing\"\n                },\n                {\n                    key: \"applications.cable-tray\"\n                },\n                {\n                    key: \"applications.equipment\"\n                },\n                {\n                    key: \"applications.structure\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"U Bolt\"\n                }\n            ],\n            features: []\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-r from-primary-600 to-primary-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-white mb-6\",\n                            children: t(\"products.categories.bolts.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-primary-100 max-w-3xl mx-auto\",\n                            children: t(\"products.categories.bolts.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 lg:py-24 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-16\",\n                        children: boltProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_ProductDetail__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                product: product\n                            }, product.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(BoltsPage, \"ot2YhC7pP10gRrIouBKIa40vomw=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage\n    ];\n});\n_c = BoltsPage;\nvar _c;\n$RefreshReg$(_c, \"BoltsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/bolts/page.tsx\n"));

/***/ })

});