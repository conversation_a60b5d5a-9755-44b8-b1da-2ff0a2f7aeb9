"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/bolts/page",{

/***/ "(app-pages-browser)/./src/app/products/bolts/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/products/bolts/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BoltsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_products_ProductDetail__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/products/ProductDetail */ \"(app-pages-browser)/./src/components/products/ProductDetail.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BoltsPage() {\n    _s();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const boltProducts = [\n        {\n            id: \"hex-bolt\",\n            slug: \"hex-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.hexBolt.name\",\n            descriptionKey: \"products.categories.bolts.hexBolt.description\",\n            shortDescKey: \"products.categories.bolts.hexBolt.description\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    valueKey: \"hexBolt.spec\"\n                },\n                {\n                    key: \"specifications.strength\",\n                    valueKey: \"hexBolt.strength\"\n                },\n                {\n                    key: \"specifications.material\",\n                    valueKey: \"hexBolt.material\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    valueKey: \"hexBolt.surface\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"mechanical\"\n                },\n                {\n                    key: \"construction\"\n                },\n                {\n                    key: \"automotive\"\n                },\n                {\n                    key: \"electrical\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"Hex Bolt\"\n                },\n                {\n                    url: \"https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n                    alt: \"Hex Bolt Detail\"\n                }\n            ],\n            features: []\n        },\n        {\n            id: \"flange-bolt\",\n            slug: \"flange-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.flangeBolt.name\",\n            descriptionKey: \"products.categories.bolts.flangeBolt.description\",\n            shortDescKey: \"products.categories.bolts.flangeBolt.description\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    valueKey: \"flangeBolt.spec\"\n                },\n                {\n                    key: \"specifications.strength\",\n                    valueKey: \"flangeBolt.strength\"\n                },\n                {\n                    key: \"specifications.material\",\n                    valueKey: \"flangeBolt.material\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    valueKey: \"flangeBolt.surface\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"automotive\"\n                },\n                {\n                    key: \"mechanical\"\n                },\n                {\n                    key: \"precision\"\n                },\n                {\n                    key: \"high-strength\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"Flange Bolt\"\n                }\n            ],\n            features: []\n        },\n        {\n            id: \"u-bolt\",\n            slug: \"u-bolt\",\n            category: \"bolts\",\n            nameKey: \"U型螺栓\",\n            descriptionKey: \"管道固定专用U型螺栓，抱箍连接，安装方便\",\n            shortDescKey: \"管道固定专用U型螺栓，抱箍连接，安装方便\",\n            specifications: [\n                {\n                    key: \"spec\",\n                    value: \"规格: M8-M30\"\n                },\n                {\n                    key: \"material\",\n                    value: \"材质: Q235, 304不锈钢\"\n                },\n                {\n                    key: \"surface\",\n                    value: \"表面处理: 镀锌、不锈钢本色\"\n                },\n                {\n                    key: \"shape\",\n                    value: \"形状: 标准U型、加长U型\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"pipe-fixing\"\n                },\n                {\n                    key: \"cable-tray\"\n                },\n                {\n                    key: \"equipment\"\n                },\n                {\n                    key: \"structure\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"U Bolt\"\n                }\n            ],\n            features: []\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-r from-primary-600 to-primary-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-white mb-6\",\n                            children: t(\"products.categories.bolts.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-primary-100 max-w-3xl mx-auto\",\n                            children: t(\"products.categories.bolts.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 lg:py-24 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-16\",\n                        children: boltProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_ProductDetail__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                product: product\n                            }, product.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(BoltsPage, \"ot2YhC7pP10gRrIouBKIa40vomw=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage\n    ];\n});\n_c = BoltsPage;\nvar _c;\n$RefreshReg$(_c, \"BoltsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHJvZHVjdHMvYm9sdHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFK0M7QUFDQTtBQUNnQjtBQUNQO0FBR3pDLFNBQVNJOztJQUN0QixNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHRixzRUFBV0E7SUFFekIsTUFBTUcsZUFBMEI7UUFDOUI7WUFDRUMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsU0FBUztZQUNUQyxnQkFBZ0I7WUFDaEJDLGNBQWM7WUFDZEMsZ0JBQWdCO2dCQUNkO29CQUFFQyxLQUFLO29CQUF1QkMsVUFBVTtnQkFBZTtnQkFDdkQ7b0JBQUVELEtBQUs7b0JBQTJCQyxVQUFVO2dCQUFtQjtnQkFDL0Q7b0JBQUVELEtBQUs7b0JBQTJCQyxVQUFVO2dCQUFtQjtnQkFDL0Q7b0JBQUVELEtBQUs7b0JBQTBCQyxVQUFVO2dCQUFrQjthQUM5RDtZQUNEQyxjQUFjO2dCQUNaO29CQUFFRixLQUFLO2dCQUFhO2dCQUNwQjtvQkFBRUEsS0FBSztnQkFBZTtnQkFDdEI7b0JBQUVBLEtBQUs7Z0JBQWE7Z0JBQ3BCO29CQUFFQSxLQUFLO2dCQUFhO2FBQ3JCO1lBQ0RHLFFBQVE7Z0JBQ047b0JBQUVDLEtBQUs7b0JBQStHQyxLQUFLO2dCQUFXO2dCQUN0STtvQkFBRUQsS0FBSztvQkFBK0dDLEtBQUs7Z0JBQWtCO2FBQzlJO1lBQ0RDLFVBQVUsRUFBRTtRQUNkO1FBQ0E7WUFDRWIsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsU0FBUztZQUNUQyxnQkFBZ0I7WUFDaEJDLGNBQWM7WUFDZEMsZ0JBQWdCO2dCQUNkO29CQUFFQyxLQUFLO29CQUF1QkMsVUFBVTtnQkFBa0I7Z0JBQzFEO29CQUFFRCxLQUFLO29CQUEyQkMsVUFBVTtnQkFBc0I7Z0JBQ2xFO29CQUFFRCxLQUFLO29CQUEyQkMsVUFBVTtnQkFBc0I7Z0JBQ2xFO29CQUFFRCxLQUFLO29CQUEwQkMsVUFBVTtnQkFBcUI7YUFDakU7WUFDREMsY0FBYztnQkFDWjtvQkFBRUYsS0FBSztnQkFBYTtnQkFDcEI7b0JBQUVBLEtBQUs7Z0JBQWE7Z0JBQ3BCO29CQUFFQSxLQUFLO2dCQUFZO2dCQUNuQjtvQkFBRUEsS0FBSztnQkFBZ0I7YUFDeEI7WUFDREcsUUFBUTtnQkFDTjtvQkFBRUMsS0FBSztvQkFBK0dDLEtBQUs7Z0JBQWM7YUFDMUk7WUFDREMsVUFBVSxFQUFFO1FBQ2Q7UUFDQTtZQUNFYixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxTQUFTO1lBQ1RDLGdCQUFnQjtZQUNoQkMsY0FBYztZQUNkQyxnQkFBZ0I7Z0JBQ2Q7b0JBQUVDLEtBQUs7b0JBQVFPLE9BQU87Z0JBQWE7Z0JBQ25DO29CQUFFUCxLQUFLO29CQUFZTyxPQUFPO2dCQUFtQjtnQkFDN0M7b0JBQUVQLEtBQUs7b0JBQVdPLE9BQU87Z0JBQWlCO2dCQUMxQztvQkFBRVAsS0FBSztvQkFBU08sT0FBTztnQkFBZ0I7YUFDeEM7WUFDREwsY0FBYztnQkFDWjtvQkFBRUYsS0FBSztnQkFBYztnQkFDckI7b0JBQUVBLEtBQUs7Z0JBQWE7Z0JBQ3BCO29CQUFFQSxLQUFLO2dCQUFZO2dCQUNuQjtvQkFBRUEsS0FBSztnQkFBWTthQUNwQjtZQUNERyxRQUFRO2dCQUNOO29CQUFFQyxLQUFLO29CQUErR0MsS0FBSztnQkFBUzthQUNySTtZQUNEQyxVQUFVLEVBQUU7UUFDZDtLQUNEO0lBRUQscUJBQ0UsOERBQUNFO1FBQUtDLFdBQVU7OzBCQUNkLDhEQUFDdkIsaUVBQU1BOzs7OzswQkFHUCw4REFBQ3dCO2dCQUFRRCxXQUFVOzBCQUNqQiw0RUFBQ0U7b0JBQUlGLFdBQVU7O3NDQUNiLDhEQUFDRzs0QkFBR0gsV0FBVTtzQ0FDWGxCLEVBQUU7Ozs7OztzQ0FFTCw4REFBQ3NCOzRCQUFFSixXQUFVO3NDQUNWbEIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVQsOERBQUNtQjtnQkFBUUQsV0FBVTswQkFDakIsNEVBQUNFO29CQUFJRixXQUFVOzhCQUNiLDRFQUFDRTt3QkFBSUYsV0FBVTtrQ0FDWmpCLGFBQWFzQixHQUFHLENBQUMsQ0FBQ0Msd0JBQ2pCLDhEQUFDM0IsMEVBQWFBO2dDQUFrQjJCLFNBQVNBOytCQUFyQkEsUUFBUXRCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU10Qyw4REFBQ04saUVBQU1BOzs7Ozs7Ozs7OztBQUdiO0dBN0d3Qkc7O1FBQ1JELGtFQUFXQTs7O0tBREhDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcHJvZHVjdHMvYm9sdHMvcGFnZS50c3g/OWQ5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlcidcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9Gb290ZXInXG5pbXBvcnQgUHJvZHVjdERldGFpbCBmcm9tICdAL2NvbXBvbmVudHMvcHJvZHVjdHMvUHJvZHVjdERldGFpbCdcbmltcG9ydCB7IHVzZUxhbmd1YWdlIH0gZnJvbSAnQC9jb250ZXh0cy9MYW5ndWFnZUNvbnRleHQnXG5pbXBvcnQgeyBQcm9kdWN0IH0gZnJvbSAnQC9kYXRhL3Byb2R1Y3RzJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBCb2x0c1BhZ2UoKSB7XG4gIGNvbnN0IHsgdCB9ID0gdXNlTGFuZ3VhZ2UoKVxuXG4gIGNvbnN0IGJvbHRQcm9kdWN0czogUHJvZHVjdFtdID0gW1xuICAgIHtcbiAgICAgIGlkOiAnaGV4LWJvbHQnLFxuICAgICAgc2x1ZzogJ2hleC1ib2x0JyxcbiAgICAgIGNhdGVnb3J5OiAnYm9sdHMnLFxuICAgICAgbmFtZUtleTogJ3Byb2R1Y3RzLmNhdGVnb3JpZXMuYm9sdHMuaGV4Qm9sdC5uYW1lJyxcbiAgICAgIGRlc2NyaXB0aW9uS2V5OiAncHJvZHVjdHMuY2F0ZWdvcmllcy5ib2x0cy5oZXhCb2x0LmRlc2NyaXB0aW9uJyxcbiAgICAgIHNob3J0RGVzY0tleTogJ3Byb2R1Y3RzLmNhdGVnb3JpZXMuYm9sdHMuaGV4Qm9sdC5kZXNjcmlwdGlvbicsXG4gICAgICBzcGVjaWZpY2F0aW9uczogW1xuICAgICAgICB7IGtleTogJ3NwZWNpZmljYXRpb25zLnNwZWMnLCB2YWx1ZUtleTogJ2hleEJvbHQuc3BlYycgfSxcbiAgICAgICAgeyBrZXk6ICdzcGVjaWZpY2F0aW9ucy5zdHJlbmd0aCcsIHZhbHVlS2V5OiAnaGV4Qm9sdC5zdHJlbmd0aCcgfSxcbiAgICAgICAgeyBrZXk6ICdzcGVjaWZpY2F0aW9ucy5tYXRlcmlhbCcsIHZhbHVlS2V5OiAnaGV4Qm9sdC5tYXRlcmlhbCcgfSxcbiAgICAgICAgeyBrZXk6ICdzcGVjaWZpY2F0aW9ucy5zdXJmYWNlJywgdmFsdWVLZXk6ICdoZXhCb2x0LnN1cmZhY2UnIH1cbiAgICAgIF0sXG4gICAgICBhcHBsaWNhdGlvbnM6IFtcbiAgICAgICAgeyBrZXk6ICdtZWNoYW5pY2FsJyB9LFxuICAgICAgICB7IGtleTogJ2NvbnN0cnVjdGlvbicgfSxcbiAgICAgICAgeyBrZXk6ICdhdXRvbW90aXZlJyB9LFxuICAgICAgICB7IGtleTogJ2VsZWN0cmljYWwnIH1cbiAgICAgIF0sXG4gICAgICBpbWFnZXM6IFtcbiAgICAgICAgeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTYwOTIwNTgwNzEwNy1lOGVjMjEyMGY5ZGU/aXhsaWI9cmItNC4wLjMmYXV0bz1mb3JtYXQmZml0PWNyb3Amdz04MDAmcT04MCcsIGFsdDogJ0hleCBCb2x0JyB9LFxuICAgICAgICB7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTgxMDk0Nzk0MzI5LWM4MTEyYTg5YWYxMj9peGxpYj1yYi00LjAuMyZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCZ3PTQwMCZxPTgwJywgYWx0OiAnSGV4IEJvbHQgRGV0YWlsJyB9XG4gICAgICBdLFxuICAgICAgZmVhdHVyZXM6IFtdXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2ZsYW5nZS1ib2x0JyxcbiAgICAgIHNsdWc6ICdmbGFuZ2UtYm9sdCcsXG4gICAgICBjYXRlZ29yeTogJ2JvbHRzJyxcbiAgICAgIG5hbWVLZXk6ICdwcm9kdWN0cy5jYXRlZ29yaWVzLmJvbHRzLmZsYW5nZUJvbHQubmFtZScsXG4gICAgICBkZXNjcmlwdGlvbktleTogJ3Byb2R1Y3RzLmNhdGVnb3JpZXMuYm9sdHMuZmxhbmdlQm9sdC5kZXNjcmlwdGlvbicsXG4gICAgICBzaG9ydERlc2NLZXk6ICdwcm9kdWN0cy5jYXRlZ29yaWVzLmJvbHRzLmZsYW5nZUJvbHQuZGVzY3JpcHRpb24nLFxuICAgICAgc3BlY2lmaWNhdGlvbnM6IFtcbiAgICAgICAgeyBrZXk6ICdzcGVjaWZpY2F0aW9ucy5zcGVjJywgdmFsdWVLZXk6ICdmbGFuZ2VCb2x0LnNwZWMnIH0sXG4gICAgICAgIHsga2V5OiAnc3BlY2lmaWNhdGlvbnMuc3RyZW5ndGgnLCB2YWx1ZUtleTogJ2ZsYW5nZUJvbHQuc3RyZW5ndGgnIH0sXG4gICAgICAgIHsga2V5OiAnc3BlY2lmaWNhdGlvbnMubWF0ZXJpYWwnLCB2YWx1ZUtleTogJ2ZsYW5nZUJvbHQubWF0ZXJpYWwnIH0sXG4gICAgICAgIHsga2V5OiAnc3BlY2lmaWNhdGlvbnMuc3VyZmFjZScsIHZhbHVlS2V5OiAnZmxhbmdlQm9sdC5zdXJmYWNlJyB9XG4gICAgICBdLFxuICAgICAgYXBwbGljYXRpb25zOiBbXG4gICAgICAgIHsga2V5OiAnYXV0b21vdGl2ZScgfSxcbiAgICAgICAgeyBrZXk6ICdtZWNoYW5pY2FsJyB9LFxuICAgICAgICB7IGtleTogJ3ByZWNpc2lvbicgfSxcbiAgICAgICAgeyBrZXk6ICdoaWdoLXN0cmVuZ3RoJyB9XG4gICAgICBdLFxuICAgICAgaW1hZ2VzOiBbXG4gICAgICAgIHsgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MDkyMDU4MDcxMDctZThlYzIxMjBmOWRlP2l4bGliPXJiLTQuMC4zJmF1dG89Zm9ybWF0JmZpdD1jcm9wJnc9ODAwJnE9ODAnLCBhbHQ6ICdGbGFuZ2UgQm9sdCcgfVxuICAgICAgXSxcbiAgICAgIGZlYXR1cmVzOiBbXVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICd1LWJvbHQnLFxuICAgICAgc2x1ZzogJ3UtYm9sdCcsXG4gICAgICBjYXRlZ29yeTogJ2JvbHRzJyxcbiAgICAgIG5hbWVLZXk6ICdV5Z6L6J665qCTJyxcbiAgICAgIGRlc2NyaXB0aW9uS2V5OiAn566h6YGT5Zu65a6a5LiT55SoVeWei+ieuuagk++8jOaKseeujei/nuaOpe+8jOWuieijheaWueS+vycsXG4gICAgICBzaG9ydERlc2NLZXk6ICfnrqHpgZPlm7rlrprkuJPnlKhV5Z6L6J665qCT77yM5oqx566N6L+e5o6l77yM5a6J6KOF5pa55L6/JyxcbiAgICAgIHNwZWNpZmljYXRpb25zOiBbXG4gICAgICAgIHsga2V5OiAnc3BlYycsIHZhbHVlOiAn6KeE5qC8OiBNOC1NMzAnIH0sXG4gICAgICAgIHsga2V5OiAnbWF0ZXJpYWwnLCB2YWx1ZTogJ+adkOi0qDogUTIzNSwgMzA05LiN6ZSI6ZKiJyB9LFxuICAgICAgICB7IGtleTogJ3N1cmZhY2UnLCB2YWx1ZTogJ+ihqOmdouWkhOeQhjog6ZWA6ZSM44CB5LiN6ZSI6ZKi5pys6ImyJyB9LFxuICAgICAgICB7IGtleTogJ3NoYXBlJywgdmFsdWU6ICflvaLnirY6IOagh+WHhlXlnovjgIHliqDplb9V5Z6LJyB9XG4gICAgICBdLFxuICAgICAgYXBwbGljYXRpb25zOiBbXG4gICAgICAgIHsga2V5OiAncGlwZS1maXhpbmcnIH0sXG4gICAgICAgIHsga2V5OiAnY2FibGUtdHJheScgfSxcbiAgICAgICAgeyBrZXk6ICdlcXVpcG1lbnQnIH0sXG4gICAgICAgIHsga2V5OiAnc3RydWN0dXJlJyB9XG4gICAgICBdLFxuICAgICAgaW1hZ2VzOiBbXG4gICAgICAgIHsgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MDkyMDU4MDcxMDctZThlYzIxMjBmOWRlP2l4bGliPXJiLTQuMC4zJmF1dG89Zm9ybWF0JmZpdD1jcm9wJnc9ODAwJnE9ODAnLCBhbHQ6ICdVIEJvbHQnIH1cbiAgICAgIF0sXG4gICAgICBmZWF0dXJlczogW11cbiAgICB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlblwiPlxuICAgICAgPEhlYWRlciAvPlxuICAgICAgXG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicmVsYXRpdmUgcHktMjAgYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnktNjAwIHRvLXByaW1hcnktODAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgbGc6dGV4dC01eGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPlxuICAgICAgICAgICAge3QoJ3Byb2R1Y3RzLmNhdGVnb3JpZXMuYm9sdHMudGl0bGUnKX1cbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1wcmltYXJ5LTEwMCBtYXgtdy0zeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAge3QoJ3Byb2R1Y3RzLmNhdGVnb3JpZXMuYm9sdHMuc3VidGl0bGUnKX1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogUHJvZHVjdHMgTGlzdCAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTE2IGxnOnB5LTI0IGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMTZcIj5cbiAgICAgICAgICAgIHtib2x0UHJvZHVjdHMubWFwKChwcm9kdWN0KSA9PiAoXG4gICAgICAgICAgICAgIDxQcm9kdWN0RGV0YWlsIGtleT17cHJvZHVjdC5pZH0gcHJvZHVjdD17cHJvZHVjdH0gLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvbWFpbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkhlYWRlciIsIkZvb3RlciIsIlByb2R1Y3REZXRhaWwiLCJ1c2VMYW5ndWFnZSIsIkJvbHRzUGFnZSIsInQiLCJib2x0UHJvZHVjdHMiLCJpZCIsInNsdWciLCJjYXRlZ29yeSIsIm5hbWVLZXkiLCJkZXNjcmlwdGlvbktleSIsInNob3J0RGVzY0tleSIsInNwZWNpZmljYXRpb25zIiwia2V5IiwidmFsdWVLZXkiLCJhcHBsaWNhdGlvbnMiLCJpbWFnZXMiLCJ1cmwiLCJhbHQiLCJmZWF0dXJlcyIsInZhbHVlIiwibWFpbiIsImNsYXNzTmFtZSIsInNlY3Rpb24iLCJkaXYiLCJoMSIsInAiLCJtYXAiLCJwcm9kdWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/bolts/page.tsx\n"));

/***/ })

});