'use client'

import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import ProductDetail from '@/components/products/ProductDetail'
import { useLanguage } from '@/contexts/LanguageContext'
import { Product } from '@/data/products'

export default function BoltsPage() {
  const { t } = useLanguage()

  const boltProducts: Product[] = [
    {
      id: 'hex-bolt',
      slug: 'hex-bolt',
      category: 'bolts',
      nameKey: 'products.categories.bolts.hexBolt.name',
      descriptionKey: 'products.categories.bolts.hexBolt.description',
      shortDescKey: 'products.categories.bolts.hexBolt.description',
      specifications: [
        { key: 'specifications.spec', valueKey: 'hexBolt.spec' },
        { key: 'specifications.strength', valueKey: 'hexBolt.strength' },
        { key: 'specifications.material', valueKey: 'hexBolt.material' },
        { key: 'specifications.surface', valueKey: 'hexBolt.surface' }
      ],
      applications: [
        { key: 'applications.mechanical' },
        { key: 'applications.construction' },
        { key: 'applications.automotive' },
        { key: 'applications.electrical' }
      ],
      images: [
        { url: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', alt: 'Hex Bolt' },
        { url: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', alt: 'Hex Bolt Detail' }
      ],
      features: []
    },
    {
      id: 'flange-bolt',
      slug: 'flange-bolt',
      category: 'bolts',
      nameKey: 'products.categories.bolts.flangeBolt.name',
      descriptionKey: 'products.categories.bolts.flangeBolt.description',
      shortDescKey: 'products.categories.bolts.flangeBolt.description',
      specifications: [
        { key: 'specifications.spec', valueKey: 'flangeBolt.spec' },
        { key: 'specifications.strength', valueKey: 'flangeBolt.strength' },
        { key: 'specifications.material', valueKey: 'flangeBolt.material' },
        { key: 'specifications.surface', valueKey: 'flangeBolt.surface' }
      ],
      applications: [
        { key: 'applications.automotive' },
        { key: 'applications.mechanical' },
        { key: 'applications.precision' },
        { key: 'applications.high-strength' }
      ],
      images: [
        { url: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', alt: 'Flange Bolt' }
      ],
      features: []
    },
    {
      id: 'u-bolt',
      slug: 'u-bolt',
      category: 'bolts',
      nameKey: 'products.bolts.uBolt.name',
      descriptionKey: 'products.bolts.uBolt.description',
      shortDescKey: 'products.bolts.uBolt.shortDesc',
      specifications: [
        { key: 'specifications.spec', valueKey: 'uBolt.spec' },
        { key: 'specifications.material', valueKey: 'uBolt.material' },
        { key: 'specifications.surface', valueKey: 'uBolt.surface' },
        { key: 'specifications.shape', valueKey: 'uBolt.shape' }
      ],
      applications: [
        { key: 'applications.pipe-fixing' },
        { key: 'applications.cable-tray' },
        { key: 'applications.equipment' },
        { key: 'applications.structure' }
      ],
      images: [
        { url: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', alt: 'U Bolt' }
      ],
      features: []
    }
  ]

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary-600 to-primary-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('products.categories.bolts.title')}
          </h1>
          <p className="text-xl text-primary-100 max-w-3xl mx-auto">
            {t('products.categories.bolts.subtitle')}
          </p>
        </div>
      </section>

      {/* Products List */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {boltProducts.map((product) => (
              <ProductDetail key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
