/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/LanguageContext.tsx */ \"(ssr)/./src/contexts/LanguageContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1pbnRlciUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIyUm9ib3RvJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyd2VpZ2h0JTIyJTNBJTVCJTIyMzAwJTIyJTJDJTIyNDAwJTIyJTJDJTIyNTAwJTIyJTJDJTIyNzAwJTIyJTVEJTJDJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTJDJTIydmFyaWFibGUlMjIlM0ElMjItLWZvbnQtcm9ib3RvJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIycm9ib3RvJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW4lNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDbHhmYXN0ZW5lciU1Q3NyYyU1Q2NvbnRleHRzJTVDTGFuZ3VhZ2VDb250ZXh0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9seGZhc3RlbmVyLXdlYnNpdGUvPzFmNzEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxseGZhc3RlbmVyXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxMYW5ndWFnZUNvbnRleHQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-roboto%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Ccontexts%5CLanguageContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _public_locales_zh_common_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/locales/zh/common.json */ \"(ssr)/./public/locales/zh/common.json\");\n/* harmony import */ var _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../public/locales/en/common.json */ \"(ssr)/./public/locales/en/common.json\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage,languages auto */ \n\n// 直接导入翻译文件\n\n\n// 翻译数据存储 - 直接使用导入的数据\nconst translations = {\n    zh: _public_locales_zh_common_json__WEBPACK_IMPORTED_MODULE_2__,\n    en: _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__\n};\n// 获取嵌套对象的值\nconst getNestedValue = (obj, path)=>{\n    return path.split(\".\").reduce((current, key)=>{\n        return current && current[key] !== undefined ? current[key] : undefined;\n    }, obj);\n};\n// 创建上下文\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"zh\");\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用 useLayoutEffect 在渲染前同步读取 localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (false) {}\n    }, []);\n    // 服务端渲染时立即标记为已初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            setIsInitialized(true);\n        }\n    }, []);\n    // 设置语言并保存到 localStorage\n    const setLanguage = (lang)=>{\n        setLanguageState(lang);\n        if (false) {}\n    };\n    // 翻译函数\n    const t = (key)=>{\n        const translation = getNestedValue(translations[language], key);\n        if (!translation) {\n            console.warn(`Translation missing for key: ${key} in language: ${language}`);\n            return key // 返回键名作为后备\n            ;\n        }\n        return translation;\n    };\n    const value = {\n        language,\n        setLanguage,\n        t\n    };\n    // 在初始化完成前不渲染内容，避免闪烁\n    if (!isInitialized) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n// 使用语言上下文的Hook\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n// 导出语言列表供其他组件使用\nconst languages = [\n    {\n        code: \"zh\",\n        name: \"简体中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9c3cc20aed3a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbHhmYXN0ZW5lci13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz82Mjc3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOWMzY2MyMGFlZDNhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Roboto\",\"arguments\":[{\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-roboto\"}],\"variableName\":\"roboto\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Roboto\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-roboto\\\"}],\\\"variableName\\\":\\\"roboto\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./src/contexts/LanguageContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"联勋紧固件有限公司 - 专业紧固件制造商\",\n    description: \"联勋紧固件有限公司是一家专业的紧固件制造商，专注于设计和生产高品质紧固件。我们提供螺栓、螺母、螺钉、螺柱、垫圈等全系列紧固件产品。\",\n    keywords: \"紧固件,螺栓,螺母,螺钉,螺柱,垫圈,联勋紧固件\",\n    authors: [\n        {\n            name: \"联勋紧固件有限公司\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"联勋紧固件有限公司 - 专业紧固件制造商\",\n        description: \"联勋紧固件有限公司是一家专业的紧固件制造商，专注于设计和生产高品质紧固件。\",\n        type: \"website\",\n        locale: \"zh_CN\",\n        siteName: \"联勋紧固件有限公司\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Roboto_arguments_weight_300_400_500_700_subsets_latin_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   languages: () => (/* binding */ e2),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#useLanguage`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\lxfastener\src\contexts\LanguageContext.tsx#languages`);


/***/ }),

/***/ "(ssr)/./public/locales/en/common.json":
/*!***************************************!*\
  !*** ./public/locales/en/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"nav":{"home":"Home","about":"About Us","products":"Products","technical":"Technical","contact":"Contact Us"},"hero":{"title1":"Professional Fastener Manufacturer","title2":"Quality Assurance, Precision Manufacturing","title3":"Global Service, Trustworthy","subtitle1":"Lianxun Fastener Co., Ltd","subtitle2":"10 Years Professional Experience","subtitle3":"Serving Global Customers","description1":"Specializing in the design, production and sales of high-quality fasteners, providing professional fastening system solutions for global customers","description2":"With advanced cold forging, hot forging workshops, heat treatment equipment, strict quality control system ensures product quality","description3":"Create benefits for customers, opportunities for employees, and value for society","cta1":"Learn More","cta2":"View Products","cta3":"Contact Us"},"about":{"title":"About Us","companyName":"Lianxun Fastener Co., Ltd","description":"Lianxun Fastener Co., Ltd is a professional Chinese fastener manufacturer specializing in the design and production of top-notch fasteners. Located in Yongnian District, Handan City - the \'Capital of Fasteners in China\', it covers a business area of 7,000 square meters including cold forging workshop, hot forging workshop, heat treatment workshop, warehouse and laboratory.","description2":"Currently, the company has more than 80 sets of production and testing equipment and more than 100 employees. We are committed to providing professional and efficient fastening system solutions for global customers. With corporate values of integrity, unity and innovation, we create benefits for customers, opportunities for employees, and value for society.","readMore":"Read More","stats":{"experience":"Years Experience","employees":"Professional Staff","equipment":"Production Equipment","facility":"Square Meters Facility"},"certification":"Quality Certification"},"products":{"title":"Products List","subtitle":"We provide a full range of high-quality fastener products to meet various industrial application needs","description":"We provide a full range of high-quality fastener products to meet various industrial application needs","viewMore":"View More Products","viewAll":"View All Products","bolts":"Bolts","nuts":"Nuts","screws":"Screws","studs":"Studs","washers":"Washers","categories":{"bolts":{"title":"Bolt Products","subtitle":"High-strength bolt products suitable for various engineering connections","hexBolt":{"name":"Hex Bolts","description":"Standard hex head bolts with strong versatility, suitable for various mechanical connections"},"flangeBolt":{"name":"Flange Bolts","description":"Bolts with flange plates to increase contact area and distribute loads"}},"nuts":{"title":"Nut Products","subtitle":"High-quality nut products, perfectly matched with bolts","hexNut":{"name":"Hex Nuts","description":"Standard hex nuts, used with bolts for reliable connections"},"flangeNut":{"name":"Flange Nuts","description":"Nuts with flange plates to increase contact area and distribute loads"}},"screws":{"title":"Screw Products","subtitle":"Precision screw products to meet various fastening needs","selfTapping":{"name":"Self-Tapping Screws","description":"Self-tapping screws that require no pre-drilling and directly tap threads for connection"},"machine":{"name":"Machine Screws","description":"Machine screws with precision threads, suitable for mechanical equipment"}},"studs":{"title":"Stud Products","subtitle":"Professional stud products providing reliable double-end connections","doubleEnd":{"name":"Double-End Studs","description":"Studs threaded at both ends for through connections"},"welding":{"name":"Welding Studs","description":"Studs specifically designed for welding connections, providing strong and reliable welds"}},"washers":{"title":"Washer Products","subtitle":"Quality washer products providing effective sealing and protection","flat":{"name":"Flat Washers","description":"Standard flat washers to increase contact area and prevent loosening"},"spring":{"name":"Spring Washers","description":"Elastic washers providing preload force to prevent bolt loosening"}}}},"specifications":{"spec":"Specification","strength":"Strength Grade","material":"Material","surface":"Surface Treatment","shape":"Shape","thickness":"Thickness","headType":"Head Type","weldingMethod":"Welding Method","length":"Length"},"applications":{"mechanical":"Mechanical Equipment","construction":"Construction Structure","automotive":"Automotive Industry","electrical":"Electrical Equipment","precision":"Precision Equipment","high-strength":"High-Strength Connection","pipe-fixing":"Pipe Fixing","cable-tray":"Cable Tray","equipment":"Equipment Installation","structure":"Structure Connection","thin-plate":"Thin Plate Connection","plastic":"Plastic Fixing","electronics":"Electronic Equipment","furniture":"Furniture Manufacturing","precision-machinery":"Precision Machinery","instruments":"Instruments","automation":"Automation Equipment","flange":"Flange Connection","pipeline":"Pipeline Connection","steel-structure":"Steel Structure","shipbuilding":"Shipbuilding","bridge":"Bridge Engineering","curtain-wall":"Curtain Wall","bolt-connection":"Bolt Connection","equipment-installation":"Equipment Installation","structural-fixing":"Structural Fixing"},"surfaceTreatments":{"zinc":"Zinc Plating","blackening":"Blackening","phosphating":"Phosphating","dacromet":"Dacromet","nickel":"Nickel Plating","stainless":"Stainless Steel Natural","passivation":"Passivation"},"hexBolt":{"spec":"M6-M36","strength":"4.8, 6.8, 8.8, 10.9, 12.9","material":"Q235, 35K, 45K, 40Cr","surface":"Zinc Plating, Blackening, Phosphating"},"flangeBolt":{"spec":"M6-M20","strength":"8.8, 10.9","material":"35K, 45K, 40Cr","surface":"Zinc Plating, Dacromet"},"uBolt":{"spec":"M8-M30","material":"Q235, 304 Stainless Steel","surface":"Zinc Plating, Stainless Steel Natural","shape":"Standard U-type, Extended U-type"},"product":{"structureBolts":{"name":"Structure Bolts","desc":"High-strength structural connection bolts for steel construction","description":"Structure bolts are high-strength fasteners specifically designed for steel structure connections, featuring excellent mechanical properties and corrosion resistance. Widely used in building steel structures, bridge engineering, tower structures and other important engineering projects.","specs":{"grade":"Strength Grade","size":"Size Range","length":"Length Range","material":"Material","surface":"Surface Treatment"},"apps":{"steel":"Steel Structure Buildings","bridge":"Bridge Engineering","tower":"Tower Structures","industrial":"Industrial Buildings"},"features":{"highStrength":"High strength design with strong load capacity","corrosionResistant":"Excellent corrosion resistance performance","precisionThreads":"Precision threads for reliable connections","qualityTested":"Strict quality testing, meets national standards"}},"shearWeldingStuds":{"name":"Shear Welding Studs","desc":"Professional welding studs providing reliable shear connections","description":"Shear welding studs are connectors fixed to steel structures through arc welding, mainly used to transfer shear forces in steel-concrete composite structures. Features fast welding, reliable connections, and convenient construction.","specs":{"diameter":"Diameter","length":"Length","material":"Material","weldTime":"Welding Time"},"apps":{"composite":"Steel-Concrete Composite Structures","deck":"Steel Bar Truss Floor Deck","precast":"Precast Component Connections"},"features":{"fastWelding":"Fast welding with high efficiency","strongShear":"Strong shear load capacity","precisePosition":"Precise positioning with stable quality"}},"hexBolts":{"name":"Hex Bolts","desc":"Standard hex head bolts widely used in mechanical connections","description":"Hex bolts are one of the most commonly used fasteners, featuring high standardization, strong versatility, and easy installation. Suitable for connections and fixings in various mechanical equipment, building structures, automotive manufacturing and other fields.","specs":{"grade":"Strength Grade","size":"Size Range","length":"Length Range","material":"Material"},"apps":{"machinery":"Mechanical Equipment","automotive":"Automotive Manufacturing","construction":"Construction Engineering","equipment":"Equipment Installation"},"features":{"versatile":"Strong versatility with wide applications","standardized":"Standardized production with good interchangeability","reliable":"Reliable connections with stable performance"}},"hexFlangeBolts":{"name":"Hex Flange Bolts","desc":"Hex bolts with flange for increased contact area"},"carriageBolts":{"name":"Carriage Bolts","desc":"Square neck bolts preventing rotation, suitable for wood connections"},"eyeBolts":{"name":"Eye Bolts","desc":"Bolts with ring head for lifting and traction applications"},"foundationAnchorBolts":{"name":"Foundation Anchor Bolts","desc":"Anchor bolts for equipment foundation fixing"},"uBolts":{"name":"U-Bolts","desc":"U-shaped bolts for pipe and round object fixing"}},"productDetail":{"specifications":"Technical Specifications","applications":"Applications","features":"Product Features","relatedProducts":"Related Products","getQuote":"Get Quote","downloadSpec":"Download Datasheet","backToProducts":"Back to Products","gallery":"Product Gallery","overview":"Product Overview"},"culture":{"title":"Corporate Culture","subtitle":"Our core values guide our development direction and shape our corporate spirit","mission":"Corporate Mission","vision":"Corporate Vision","values":"Corporate Values","missionDesc":"Provide professional and efficient fastening system solutions for customers worldwide","visionDesc":"Create benefits for customers, opportunities for employees, and value for society","valuesDesc":"Honesty, Trustworthiness, Unity, Innovation","quality":{"title":"Quality Commitment","description":"We strictly implement the ISO 9001:2015 quality management system standards. From raw material procurement to finished product delivery, every link undergoes strict quality control. Our laboratory is equipped with advanced testing equipment to ensure that every product meets international standards and customer requirements.","certification":"ISO 9001:2015 Quality Management System Certification","materials":"Strict raw material inspection standards","process":"Perfect production process control","testing":"Comprehensive finished product testing system"}},"technical":{"title":"Technical Support","subtitle":"Professional technical documentation and solutions","description":"We provide comprehensive technical support and professional guidance to help you choose the most suitable fastener solutions","featuredArticle":"Featured Article","technicalDocs":"Technical Documentation","readFull":"Read Full Article","readMore":"Read More","readTime":"read","downloadTitle":"Technical Documentation Download","downloadDesc":"We provide complete product technical documentation and specification downloads, including CAD drawings, technical parameter tables, installation guides, etc.","downloadBtn":"Download Technical Documentation Package","categories":{"guide":"Technical Guide","standard":"Standards & Specifications","data":"Technical Data","calculation":"Calculation Methods","analysis":"Case Analysis"},"articles":{"coarseVsFine":{"title":"Comparison of Coarse and Fine Threads","excerpt":"Are coarse threads better or fine threads better? This is a question we often hear about inserts and external threaded fasteners. This article will analyze the characteristics, application scenarios and selection criteria of both thread types.","date":"January 6, 2025"},"surfaceTreatment":{"title":"European Surface Treatment Codes and Standards","excerpt":"Detailed introduction to the code system and technical specifications for fastener surface treatment in European standards, including standard requirements for various surface treatment processes such as galvanizing, blackening, and phosphating.","date":"January 6, 2025"},"hardnessConversion":{"title":"Hardness Conversion Chart","excerpt":"Approximate conversion values for steel Rockwell C hardness, including comparison relationships between HRC, HV, HB and other hardness standards, as well as the application range of various hardness testing methods.","date":"January 6, 2025"},"materialSelection":{"title":"Fastener Material Selection Guide","excerpt":"How to select appropriate fastener materials based on application environment, including the characteristics and applicable scenarios of carbon steel, alloy steel, stainless steel and other materials.","date":"January 5, 2025"},"boltPreload":{"title":"Bolt Preload Calculation Methods","excerpt":"Detailed introduction to bolt preload calculation methods, influencing factors and control standards to ensure connection reliability and safety.","date":"January 4, 2025"},"failureAnalysis":{"title":"Fastener Failure Analysis Cases","excerpt":"Analysis of fastener failure causes through actual cases, including preventive measures for common problems such as fatigue fracture, corrosion failure, and overload fracture.","date":"January 3, 2025"}}},"contact":{"title":"Contact Us","subtitle":"We look forward to working with you to provide professional fastener solutions","info":{"title":"Contact Information","description":"We are always ready to provide you with professional services and support. Whether you have any questions about our products or need customized fastener solutions, please feel free to contact us.","address":"Company Address","phone":"Phone","mobile":"Mobile/WeChat","email":"Email","salesEmail":"Sales Email","workTime":"Working Hours","location":"Company Location","mapLoading":"Loading map...","businessHours":"Business Hours","monday":"Monday - Friday","saturday":"Saturday","sunday":"Sunday","closed":"Closed"},"form":{"title":"Send Message","name":"Name","email":"Email","phone":"Phone","company":"Company Name","subject":"Subject","message":"Message","required":"*","placeholders":{"name":"Please enter your name","email":"Please enter your email","phone":"Please enter your phone","company":"Please enter your company name","subject":"Please select a subject","message":"Please describe your requirements or questions in detail..."},"subjects":{"select":"Please select a subject","product":"Product Inquiry","technical":"Technical Support","quotation":"Quotation Request","partnership":"Partnership","other":"Other"},"submit":"Send Message","submitting":"Sending...","success":{"title":"Message sent successfully!","message":"Thank you for your inquiry, we will reply within 24 hours."}}},"footer":{"address":"Yongnian District, Handan City, Hebei Province, China","description":"Professional industrial fastener manufacturer specializing in the design and production of structural fasteners of all kinds. Over 10 years\' experience in providing our customers with high quality materials at competitive prices and best service.","productCategories":"Product Categories","quickLinks":"Quick Links","getInTouch":"Get In Touch","copyright":"© 2024 Lianxun Fastener Co., Ltd. All Rights Reserved."}}');

/***/ }),

/***/ "(ssr)/./public/locales/zh/common.json":
/*!***************************************!*\
  !*** ./public/locales/zh/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"nav":{"home":"首页","about":"关于我们","products":"产品中心","technical":"技术支持","contact":"联系我们"},"hero":{"title1":"专业紧固件制造商","title2":"品质保证 精工制造","title3":"全球服务 值得信赖","subtitle1":"联勋紧固件有限公司","subtitle2":"10年专业经验","subtitle3":"服务全球客户","description1":"专注于高品质紧固件的设计、生产和销售，为全球客户提供专业的紧固系统解决方案","description2":"拥有先进的冷锻、热锻车间，热处理设备，严格的质量控制体系确保产品品质","description3":"为客户创造效益，为员工创造机会，为社会创造价值","cta1":"了解更多","cta2":"查看产品","cta3":"联系我们"},"about":{"title":"关于我们","companyName":"联勋紧固件有限公司","description":"联勋紧固件有限公司是一家专业的中国紧固件制造商，专注于设计和生产顶级紧固件。公司位于\\"中国紧固件之都\\"——河北省邯郸市永年区，占地面积7000平方米，包括冷锻车间、热锻车间、热处理车间、仓库和实验室。","description2":"目前，公司拥有80多套生产和检测设备，员工100多人。我们致力于为全球客户提供专业高效的紧固系统解决方案，以诚信、团结、创新的企业价值观，为客户创造效益，为员工创造机会，为社会创造价值。","readMore":"了解更多","stats":{"experience":"年专业经验","employees":"专业员工","equipment":"生产设备","facility":"平方米厂房"},"certification":"质量认证"},"products":{"title":"产品列表","subtitle":"我们提供全系列高品质紧固件产品，满足各种工业应用需求","description":"我们提供全系列高品质紧固件产品，满足各种工业应用需求","viewMore":"查看更多产品","viewAll":"查看全部产品","bolts":"螺栓","nuts":"螺母","screws":"螺钉","studs":"螺柱","washers":"垫圈","categories":{"bolts":{"title":"螺栓产品","subtitle":"高强度螺栓产品，适用于各种工程连接","hexBolt":{"name":"六角螺栓","description":"标准六角头螺栓，通用性强，适用于各种机械连接"},"flangeBolt":{"name":"法兰螺栓","description":"带法兰盘的螺栓，增大接触面积，分散载荷"}},"nuts":{"title":"螺母产品","subtitle":"高品质螺母产品，与螺栓完美配套","hexNut":{"name":"六角螺母","description":"标准六角螺母，与螺栓配套使用，连接可靠"},"flangeNut":{"name":"法兰螺母","description":"带法兰盘的螺母，增大接触面积，分散载荷"}},"screws":{"title":"螺钉产品","subtitle":"精密螺钉产品，满足各种固定需求","selfTapping":{"name":"自攻螺钉","description":"自攻螺钉，无需预钻孔，直接攻丝连接"},"machine":{"name":"机器螺钉","description":"机器螺钉，精密螺纹，适用于机械设备"}},"studs":{"title":"螺柱产品","subtitle":"专业螺柱产品，提供可靠的双端连接","doubleEnd":{"name":"双头螺柱","description":"两端均为螺纹的螺柱，用于穿透连接"},"welding":{"name":"焊接螺柱","description":"专用于焊接连接的螺柱，焊接牢固可靠"}},"washers":{"title":"垫圈产品","subtitle":"优质垫圈产品，提供有效的密封和保护","flat":{"name":"平垫圈","description":"标准平垫圈，增大接触面积，防止松动"},"spring":{"name":"弹簧垫圈","description":"弹性垫圈，提供预紧力，防止螺栓松动"}}}},"specifications":{"spec":"规格","strength":"强度等级","material":"材质","surface":"表面处理","shape":"形状","thickness":"厚度","headType":"头型","weldingMethod":"焊接方式","length":"长度"},"applications":{"mechanical":"机械设备","construction":"建筑结构","automotive":"汽车工业","electrical":"电力设备","precision":"精密设备","high-strength":"高强度连接","pipe-fixing":"管道固定","cable-tray":"电缆桥架","equipment":"设备安装","structure":"结构连接","thin-plate":"薄板连接","plastic":"塑料固定","electronics":"电子设备","furniture":"家具制造","precision-machinery":"精密机械","instruments":"仪器仪表","automation":"自动化设备","flange":"法兰连接","pipeline":"管道连接","steel-structure":"钢结构","shipbuilding":"船舶制造","bridge":"桥梁工程","curtain-wall":"建筑幕墙","bolt-connection":"螺栓连接","equipment-installation":"设备安装","structural-fixing":"结构固定"},"surfaceTreatments":{"zinc":"镀锌","blackening":"发黑","phosphating":"磷化","dacromet":"达克罗","nickel":"镀镍","stainless":"不锈钢本色","passivation":"钝化"},"hexBolt":{"spec":"M6-M36","strength":"4.8, 6.8, 8.8, 10.9, 12.9","material":"Q235, 35K, 45K, 40Cr","surface":"镀锌、发黑、磷化"},"flangeBolt":{"spec":"M6-M20","strength":"8.8, 10.9","material":"35K, 45K, 40Cr","surface":"镀锌、达克罗"},"uBolt":{"spec":"M8-M30","material":"Q235, 304不锈钢","surface":"镀锌、不锈钢本色","shape":"标准U型、加长U型"},"product":{"structureBolts":{"name":"结构螺栓","desc":"高强度结构连接螺栓，适用于钢结构建筑","description":"结构螺栓是专为钢结构连接设计的高强度紧固件，具有优异的机械性能和耐腐蚀性能。广泛应用于建筑钢结构、桥梁工程、塔架结构等重要工程项目中。","specs":{"grade":"强度等级","size":"规格尺寸","length":"长度范围","material":"材质","surface":"表面处理"},"apps":{"steel":"钢结构建筑","bridge":"桥梁工程","tower":"塔架结构","industrial":"工业厂房"},"features":{"highStrength":"高强度设计，承载能力强","corrosionResistant":"优异的防腐蚀性能","precisionThreads":"精密螺纹，连接可靠","qualityTested":"严格质量检测，符合国标"}},"shearWeldingStuds":{"name":"剪切焊接螺柱","desc":"专业焊接螺柱，提供可靠的剪切连接","description":"剪切焊接螺柱是一种通过电弧焊接固定在钢结构上的连接件，主要用于钢-混凝土组合结构中传递剪力。具有焊接快速、连接可靠、施工方便等特点。","specs":{"diameter":"直径","length":"长度","material":"材质","weldTime":"焊接时间"},"apps":{"composite":"钢混组合结构","deck":"钢筋桁架楼承板","precast":"预制构件连接"},"features":{"fastWelding":"快速焊接，效率高","strongShear":"剪切承载力强","precisePosition":"定位精确，质量稳定"}},"hexBolts":{"name":"六角螺栓","desc":"标准六角头螺栓，广泛应用于机械连接","description":"六角螺栓是最常用的紧固件之一，具有标准化程度高、通用性强、安装方便等特点。适用于各种机械设备、建筑结构、汽车制造等领域的连接固定。","specs":{"grade":"强度等级","size":"规格尺寸","length":"长度范围","material":"材质"},"apps":{"machinery":"机械设备","automotive":"汽车制造","construction":"建筑工程","equipment":"设备安装"},"features":{"versatile":"通用性强，应用广泛","standardized":"标准化生产，互换性好","reliable":"连接可靠，性能稳定"}},"hexFlangeBolts":{"name":"六角法兰螺栓","desc":"带法兰盘的六角螺栓，增大接触面积"},"carriageBolts":{"name":"马车螺栓","desc":"方颈螺栓，防止转动，适用于木材连接"},"eyeBolts":{"name":"吊环螺栓","desc":"带环形头部的螺栓，用于起重和牵引"},"foundationAnchorBolts":{"name":"地脚锚栓","desc":"用于设备固定的地脚螺栓"},"uBolts":{"name":"U型螺栓","desc":"U形螺栓，用于管道和圆形物体固定"}},"productDetail":{"specifications":"技术规格","applications":"应用领域","features":"产品特点","relatedProducts":"相关产品","getQuote":"获取报价","downloadSpec":"下载规格书","backToProducts":"返回产品列表","gallery":"产品图册","overview":"产品概述"},"culture":{"title":"企业文化","subtitle":"我们的核心价值观指引着我们的发展方向，塑造着我们的企业精神","mission":"企业使命","vision":"企业愿景","values":"企业价值观","missionDesc":"为全球客户提供专业高效的紧固系统解决方案","visionDesc":"为客户创造效益，为员工创造机会，为社会创造价值","valuesDesc":"诚信、守信、团结、创新","quality":{"title":"质量承诺","description":"我们严格按照ISO 9001:2015质量管理体系标准执行，从原材料采购到成品出厂，每一个环节都经过严格的质量控制。我们的实验室配备了先进的检测设备，确保每一件产品都符合国际标准和客户要求。","certification":"ISO 9001:2015 质量管理体系认证","materials":"严格的原材料检验标准","process":"完善的生产过程控制","testing":"全面的成品检测体系"}},"technical":{"title":"技术支持","subtitle":"专业的技术文档和解决方案","description":"我们提供全面的技术支持和专业指导，帮助您选择最适合的紧固件解决方案","featuredArticle":"精选文章","technicalDocs":"技术文档","readFull":"阅读全文","readMore":"阅读更多","readTime":"阅读","downloadTitle":"技术资料下载","downloadDesc":"我们提供完整的产品技术资料和规格书下载，包括CAD图纸、技术参数表、安装指南等。","downloadBtn":"下载技术资料包","categories":{"guide":"技术指南","standard":"标准规范","data":"技术资料","calculation":"计算方法","analysis":"案例分析"},"articles":{"coarseVsFine":{"title":"粗牙螺纹与细牙螺纹的比较","excerpt":"粗牙螺纹好还是细牙螺纹好？这是我们公司经常听到的关于嵌件和外螺纹紧固件的问题。本文将详细分析两种螺纹的特点、应用场景和选择标准。","date":"2025年1月6日"},"surfaceTreatment":{"title":"欧洲表面处理代码和规范","excerpt":"详细介绍欧洲标准中关于紧固件表面处理的代码体系和技术规范，包括镀锌、发黑、磷化等各种表面处理工艺的标准要求。","date":"2025年1月6日"},"hardnessConversion":{"title":"硬度换算对照表","excerpt":"钢材洛氏C硬度的近似换算值，包括HRC、HV、HB等不同硬度标准的对照关系，以及各种硬度测试方法的应用范围。","date":"2025年1月6日"},"materialSelection":{"title":"紧固件材料选择指南","excerpt":"如何根据应用环境选择合适的紧固件材料，包括碳钢、合金钢、不锈钢等材料的特性和适用场景。","date":"2025年1月5日"},"boltPreload":{"title":"螺栓预紧力计算方法","excerpt":"详细介绍螺栓预紧力的计算方法、影响因素和控制标准，确保连接的可靠性和安全性。","date":"2025年1月4日"},"failureAnalysis":{"title":"紧固件失效分析案例","excerpt":"通过实际案例分析紧固件失效的原因，包括疲劳断裂、腐蚀失效、过载断裂等常见问题的预防措施。","date":"2025年1月3日"}}},"contact":{"title":"联系我们","subtitle":"我们期待与您合作，为您提供专业的紧固件解决方案","info":{"title":"联系信息","description":"我们随时准备为您提供专业的服务和支持。无论您有任何关于产品的问题，或者需要定制化的紧固件解决方案，请随时与我们联系。","address":"公司地址","phone":"联系电话","mobile":"手机/微信","email":"邮箱地址","salesEmail":"销售邮箱","workTime":"工作时间","location":"公司位置","mapLoading":"地图加载中...","businessHours":"营业时间","monday":"周一 - 周五","saturday":"周六","sunday":"周日","closed":"休息"},"form":{"title":"发送消息","name":"姓名","email":"邮箱","phone":"电话","company":"公司名称","subject":"咨询主题","message":"详细信息","required":"*","placeholders":{"name":"请输入您的姓名","email":"请输入您的邮箱","phone":"请输入您的电话","company":"请输入您的公司名称","subject":"请选择咨询主题","message":"请详细描述您的需求或问题..."},"subjects":{"select":"请选择咨询主题","product":"产品咨询","technical":"技术支持","quotation":"报价询问","partnership":"合作洽谈","other":"其他"},"submit":"发送消息","submitting":"发送中...","success":{"title":"消息发送成功！","message":"感谢您的咨询，我们会在24小时内回复您。"}}},"footer":{"address":"中国河北省邯郸市永年区刘营镇五里村工业区","description":"专业的工业紧固件制造商，专注于各类结构紧固件的设计与生产。拥有超过10年的经验，为客户提供高品质材料、具有竞争力的价格和最佳服务。","productCategories":"产品分类","quickLinks":"快速链接","getInTouch":"联系我们","copyright":"© 2024 联勋紧固件有限公司. 保留所有权利."}}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdmin%5CDocuments%5Caugment-projects%5Clxfastener&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();