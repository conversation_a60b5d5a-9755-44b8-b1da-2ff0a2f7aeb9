'use client'

import { useState } from 'react'
import Link from 'next/link'
import { ChevronLeft, ChevronRight, Check, ArrowLeft, Download, MessageCircle } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'
import { Product } from '@/data/products'

type ProductDetailProps = {
  product: Product
  showBreadcrumb?: boolean
}

const ProductDetail = ({ product, showBreadcrumb = false }: ProductDetailProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const { t } = useLanguage()

  // 兼容新旧数据结构
  const allImages = product.images || [
    { url: (product as any).image, alt: 'Product Image' },
    ...((product as any).gallery || []).map((url: string, index: number) => ({
      url,
      alt: `Product Image ${index + 2}`
    }))
  ].filter(img => img.url)

  const nextImage = () => {
    if (allImages.length > 0) {
      setCurrentImageIndex((prev) => (prev + 1) % allImages.length)
    }
  }

  const prevImage = () => {
    if (allImages.length > 0) {
      setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length)
    }
  }

  return (
    <div className="space-y-8">
      {/* Breadcrumb */}
      {showBreadcrumb && (
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="/products" className="hover:text-primary-600 transition-colors">
            {t('nav.products')}
          </Link>
          <span>/</span>
          <span className="text-gray-900">{t(product.nameKey)}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
      {/* Product Images */}
      <div className="space-y-4">
        {/* Main Image */}
        <div className="relative aspect-square rounded-xl overflow-hidden shadow-lg group">
          {allImages.length > 0 && allImages[currentImageIndex] ? (
            <img
              src={allImages[currentImageIndex].url}
              alt={allImages[currentImageIndex].alt || t(product.nameKey || (product as any).name)}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">暂无图片</span>
            </div>
          )}
          
          {allImages.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              
              <button
                onClick={nextImage}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </>
          )}
        </div>

        {/* Thumbnail Gallery */}
        {allImages.length > 1 && (
          <div className="flex space-x-2 overflow-x-auto">
            {allImages.map((image, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                  index === currentImageIndex 
                    ? 'border-primary-500 ring-2 ring-primary-200' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <img
                  src={image.url}
                  alt={image.alt || t(product.nameKey || (product as any).name)}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Product Information */}
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {product.nameKey ? t(product.nameKey) : (product as any).name}
          </h2>
          <p className="text-gray-600 text-lg leading-relaxed">
            {product.descriptionKey ? t(product.descriptionKey) : (product as any).description}
          </p>
        </div>

        {/* Specifications */}
        <div>
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {t('productDetail.specifications')}
          </h3>
          <div className="space-y-3">
            {(product.specifications || (product as any).specifications || []).map((spec: any, index: number) => (
              <div key={index} className="flex items-start space-x-3">
                <Check className="h-5 w-5 text-primary-600 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  {typeof spec === 'object' && spec.key ? (
                    <>
                      <span className="font-medium text-gray-900">{t(spec.key)}:</span>
                      <span className="text-gray-600 ml-2">
                        {spec.valueKey ? t(spec.valueKey) : spec.value}
                      </span>
                    </>
                  ) : (
                    <span className="text-gray-600">{spec}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Applications */}
        <div>
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {t('productDetail.applications')}
          </h3>
          <div className="grid grid-cols-2 gap-3">
            {(product.applications || (product as any).applications || []).map((app: any, index: number) => (
              <div key={index} className="bg-gray-50 px-4 py-3 rounded-lg text-gray-700 text-sm font-medium">
                {typeof app === 'object' && app.key ? t(app.key) : app}
              </div>
            ))}
          </div>
        </div>

        {/* Features */}
        {product.features && product.features.length > 0 && (
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {t('productDetail.features')}
            </h3>
            <div className="space-y-2">
              {product.features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <Check className="h-5 w-5 text-primary-600 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">{t(feature)}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6">
          <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex-1 flex items-center justify-center space-x-2">
            <MessageCircle className="h-5 w-5" />
            <span>{t('productDetail.getQuote')}</span>
          </button>
          <button className="border border-primary-600 text-primary-600 hover:bg-primary-50 px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex-1 flex items-center justify-center space-x-2">
            <Download className="h-5 w-5" />
            <span>{t('productDetail.downloadSpec')}</span>
          </button>
        </div>
      </div>
    </div>
    </div>
  )
}

export default ProductDetail
