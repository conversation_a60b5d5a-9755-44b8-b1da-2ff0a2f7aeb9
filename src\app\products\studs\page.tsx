'use client'

import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import ProductDetail from '@/components/products/ProductDetail'
import { useLanguage } from '@/contexts/LanguageContext'

export default function StudsPage() {
  const { t } = useLanguage()

  const studProducts = [
  {
    id: 1,
    name: t('products.categories.studs.doubleEnd.name'),
    description: t('products.categories.studs.doubleEnd.description'),
    specifications: [
      '规格: M6-M30',
      '长度: 20-300mm',
      '材质: Q235, 35K, 45K',
      '表面处理: 镀锌、发黑'
    ],
    applications: [
      '法兰连接',
      '设备安装',
      '管道连接',
      '结构固定'
    ],
    image: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    gallery: [
      'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    ]
  },
  {
    id: 2,
    name: t('products.categories.studs.welding.name'),
    description: t('products.categories.studs.welding.description'),
    specifications: [
      '规格: M6-M20',
      '材质: Q235, 304不锈钢',
      '焊接方式: 电弧焊、储能焊',
      '表面处理: 镀锌、不锈钢本色'
    ],
    applications: [
      '钢结构',
      '船舶制造',
      '桥梁工程',
      '建筑幕墙'
    ],
    image: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    gallery: [
      'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    ]
  }
]

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary-600 to-primary-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('products.categories.studs.title')}
          </h1>
          <p className="text-xl text-primary-100 max-w-3xl mx-auto">
            {t('products.categories.studs.subtitle')}
          </p>
        </div>
      </section>

      {/* Products List */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {studProducts.map((product) => (
              <ProductDetail key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
