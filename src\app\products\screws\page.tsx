'use client'

import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import ProductDetail from '@/components/products/ProductDetail'
import { useLanguage } from '@/contexts/LanguageContext'

export default function ScrewsPage() {
  const { t } = useLanguage()

  const screwProducts = [
    {
      id: 1,
      name: t('products.categories.screws.selfTapping.name'),
      description: t('products.categories.screws.selfTapping.description'),
      specifications: [
        '规格: ST2.2-ST6.3',
        '头型: 盘头、沉头、半沉头',
        '材质: C1022, 304不锈钢',
        '表面处理: 镀锌、镀镍、不锈钢本色'
      ],
      applications: [
        '薄板连接',
        '塑料固定',
        '电子设备',
        '家具制造'
      ],
      image: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gallery: [
        'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      ]
    },
    {
      id: 2,
      name: t('products.categories.screws.machine.name'),
      description: t('products.categories.screws.machine.description'),
      specifications: [
        '规格: M2-M12',
        '头型: 内六角、十字槽、一字槽',
        '材质: 45K, 304不锈钢',
        '表面处理: 发黑、镀锌、不锈钢本色'
      ],
      applications: [
        '精密机械',
        '电子设备',
        '仪器仪表',
        '自动化设备'
      ],
      image: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gallery: [
        'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      ]
    }
  ]

  return (
    <main className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary-600 to-primary-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('products.categories.screws.title')}
          </h1>
          <p className="text-xl text-primary-100 max-w-3xl mx-auto">
            {t('products.categories.screws.subtitle')}
          </p>
        </div>
      </section>

      {/* Products List */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {screwProducts.map((product) => (
              <ProductDetail key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}