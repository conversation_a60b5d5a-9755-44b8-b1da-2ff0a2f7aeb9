globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/products/nuts/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/LanguageContext.tsx":{"*":{"id":"(ssr)/./src/contexts/LanguageContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.tsx":{"*":{"id":"(ssr)/./src/components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/products/ProductCategories.tsx":{"*":{"id":"(ssr)/./src/components/products/ProductCategories.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/nuts/page.tsx":{"*":{"id":"(ssr)/./src/app/products/nuts/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Roboto\",\"arguments\":[{\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-roboto\"}],\"variableName\":\"roboto\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Roboto\",\"arguments\":[{\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-roboto\"}],\"variableName\":\"roboto\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\contexts\\LanguageContext.tsx":{"id":"(app-pages-browser)/./src/contexts/LanguageContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\AboutSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\CorporateCultureSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\HeroSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\ProductsSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\home\\TechnicalSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\layout\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/layout/Footer.tsx","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\layout\\Header.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header.tsx","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\components\\products\\ProductCategories.tsx":{"id":"(app-pages-browser)/./src/components/products/ProductCategories.tsx","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\nuts\\page.tsx":{"id":"(app-pages-browser)/./src/app/products/nuts/page.tsx","name":"*","chunks":["app/products/nuts/page","static/chunks/app/products/nuts/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\lxfastener\\src\\app\\products\\nuts\\page":[]}}