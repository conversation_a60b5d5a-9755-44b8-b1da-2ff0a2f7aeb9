'use client'

import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import ProductDetail from '@/components/products/ProductDetail'
import { useLanguage } from '@/contexts/LanguageContext'

export default function WashersPage() {
  const { t } = useLanguage()

  const washerProducts = [
    {
      id: 1,
      name: t('products.categories.washers.flat.name'),
      description: t('products.categories.washers.flat.description'),
    specifications: [
      '规格: M6-M36',
      '厚度: 1.6-6mm',
      '材质: Q235, 65Mn, 304不锈钢',
      '表面处理: 镀锌、发黑、不锈钢本色'
    ],
    applications: [
      '螺栓连接',
      '设备安装',
      '管道连接',
      '结构固定'
    ],
    image: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    gallery: [
      'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    ]
  },
  {
    id: 2,
    name: t('products.categories.washers.spring.name'),
    description: t('products.categories.washers.spring.description'),
    specifications: [
      '规格: M6-M30',
      '材质: 65Mn弹簧钢',
      '硬度: HRC44-51',
      '表面处理: 镀锌、发黑'
    ],
    applications: [
      '防松连接',
      '振动环境',
      '机械设备',
      '汽车工业'
    ],
    image: 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    gallery: [
      'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    ]
  }
  ]

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary-600 to-primary-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('products.categories.washers.title')}
          </h1>
          <p className="text-xl text-primary-100 max-w-3xl mx-auto">
            {t('products.categories.washers.subtitle')}
          </p>
        </div>
      </section>

      {/* Products List */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {washerProducts.map((product) => (
              <ProductDetail key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
