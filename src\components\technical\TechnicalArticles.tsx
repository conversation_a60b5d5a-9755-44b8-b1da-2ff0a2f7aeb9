'use client'

import Link from 'next/link'
import { Calendar, ArrowRight, FileText, Settings, BookOpen, Download } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'

const articles = [
  {
    id: 1,
    titleKey: 'technical.articles.coarseVsFine.title',
    excerptKey: 'technical.articles.coarseVsFine.excerpt',
    dateKey: 'technical.articles.coarseVsFine.date',
    categoryKey: 'technical.categories.guide',
    readTime: '5',
    href: '/technical/coarse-vs-fine-threads',
    icon: Settings,
    featured: true
  },
  {
    id: 2,
    titleKey: 'technical.articles.surfaceTreatment.title',
    excerptKey: 'technical.articles.surfaceTreatment.excerpt',
    dateKey: 'technical.articles.surfaceTreatment.date',
    categoryKey: 'technical.categories.standard',
    readTime: '8',
    href: '/technical/european-surface-treatment',
    icon: FileText,
    featured: false
  },
  {
    id: 3,
    titleKey: 'technical.articles.hardnessConversion.title',
    excerptKey: 'technical.articles.hardnessConversion.excerpt',
    dateKey: 'technical.articles.hardnessConversion.date',
    categoryKey: 'technical.categories.data',
    readTime: '3',
    href: '/technical/hardness-conversion',
    icon: BookOpen,
    featured: false
  },
  {
    id: 4,
    titleKey: 'technical.articles.materialSelection.title',
    excerptKey: 'technical.articles.materialSelection.excerpt',
    dateKey: 'technical.articles.materialSelection.date',
    categoryKey: 'technical.categories.guide',
    readTime: '10',
    href: '/technical/material-selection',
    icon: Settings,
    featured: false
  },
  {
    id: 5,
    titleKey: 'technical.articles.boltPreload.title',
    excerptKey: 'technical.articles.boltPreload.excerpt',
    dateKey: 'technical.articles.boltPreload.date',
    categoryKey: 'technical.categories.calculation',
    readTime: '12',
    href: '/technical/bolt-preload-calculation',
    icon: BookOpen,
    featured: false
  },
  {
    id: 6,
    titleKey: 'technical.articles.failureAnalysis.title',
    excerptKey: 'technical.articles.failureAnalysis.excerpt',
    dateKey: 'technical.articles.failureAnalysis.date',
    categoryKey: 'technical.categories.analysis',
    readTime: '15',
    href: '/technical/failure-analysis',
    icon: FileText,
    featured: false
  }
]

const TechnicalArticles = () => {
  const { t } = useLanguage()
  const featuredArticle = articles.find(article => article.featured)
  const regularArticles = articles.filter(article => !article.featured)

  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Featured Article */}
        {featuredArticle && (
          <div className="mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">{t('technical.featuredArticle')}</h2>
            <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-2xl p-8 lg:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                  <div className="flex items-center space-x-4 mb-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                      {t(featuredArticle.categoryKey)}
                    </span>
                    <span className="text-gray-500 text-sm">{featuredArticle.readTime}{t('technical.readTime')}</span>
                  </div>

                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    {t(featuredArticle.titleKey)}
                  </h3>

                  <p className="text-gray-600 leading-relaxed mb-6">
                    {t(featuredArticle.excerptKey)}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-500 text-sm">
                      <Calendar className="h-4 w-4 mr-1" />
                      {t(featuredArticle.dateKey)}
                    </div>

                    <Link
                      href={featuredArticle.href}
                      className="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 group"
                    >
                      {t('technical.readFull')}
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </div>
                
                <div className="aspect-video rounded-xl overflow-hidden shadow-lg">
                  <div 
                    className="w-full h-full bg-cover bg-center bg-no-repeat"
                    style={{
                      backgroundImage: `url('https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')`
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Regular Articles */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-8">{t('technical.technicalDocs')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularArticles.map((article) => {
              const IconComponent = article.icon
              return (
                <article
                  key={article.id}
                  className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group border border-gray-100"
                >
                  <div className="p-6">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-4">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {t(article.categoryKey)}
                      </span>
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-primary-100 transition-colors">
                        <IconComponent className="h-5 w-5 text-gray-600 group-hover:text-primary-600 transition-colors" />
                      </div>
                    </div>

                    {/* Title */}
                    <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors line-clamp-2">
                      {t(article.titleKey)}
                    </h3>

                    {/* Excerpt */}
                    <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
                      {t(article.excerptKey)}
                    </p>

                    {/* Footer */}
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center text-gray-500">
                        <Calendar className="h-4 w-4 mr-1" />
                        {t(article.dateKey)}
                      </div>
                      <span className="text-gray-500">{article.readTime}{t('technical.readTime')}</span>
                    </div>

                    {/* Read More Link */}
                    <Link
                      href={article.href}
                      className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium text-sm mt-4 group-hover:translate-x-1 transition-all"
                    >
                      {t('technical.readMore')}
                      <ArrowRight className="ml-1 h-3 w-3" />
                    </Link>
                  </div>
                </article>
              )
            })}
          </div>
        </div>

        {/* Download Section */}
        <div className="mt-16 bg-gray-50 rounded-2xl p-8">
          <div className="text-center">
            <Download className="h-12 w-12 text-primary-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {t('technical.downloadTitle')}
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              {t('technical.downloadDesc')}
            </p>
            <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200">
              {t('technical.downloadBtn')}
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TechnicalArticles
