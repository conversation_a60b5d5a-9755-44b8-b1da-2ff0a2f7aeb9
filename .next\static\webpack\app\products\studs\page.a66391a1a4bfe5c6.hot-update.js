"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/studs/page",{

/***/ "(app-pages-browser)/./src/components/products/ProductDetail.tsx":
/*!***************************************************!*\
  !*** ./src/components/products/ProductDetail.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Download,MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ProductDetail = (param)=>{\n    let { product, showBreadcrumb = false } = param;\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    // 兼容新旧数据结构\n    const allImages = product.images || [\n        {\n            url: product.image,\n            alt: \"Product Image\"\n        },\n        ...(product.gallery || []).map((url, index)=>({\n                url,\n                alt: \"Product Image \".concat(index + 2)\n            }))\n    ].filter((img)=>img.url);\n    const nextImage = ()=>{\n        if (allImages.length > 0) {\n            setCurrentImageIndex((prev)=>(prev + 1) % allImages.length);\n        }\n    };\n    const prevImage = ()=>{\n        if (allImages.length > 0) {\n            setCurrentImageIndex((prev)=>(prev - 1 + allImages.length) % allImages.length);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            showBreadcrumb && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/products\",\n                        className: \"hover:text-primary-600 transition-colors\",\n                        children: t(\"nav.products\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"/\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-900\",\n                        children: t(product.nameKey)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative aspect-square rounded-xl overflow-hidden shadow-lg group\",\n                                children: [\n                                    allImages.length > 0 && allImages[currentImageIndex] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: allImages[currentImageIndex].url,\n                                        alt: allImages[currentImageIndex].alt || t(product.nameKey || product.name),\n                                        className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-gray-200 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"暂无图片\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    allImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: prevImage,\n                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: nextImage,\n                                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 9\n                            }, undefined),\n                            allImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 overflow-x-auto\",\n                                children: allImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentImageIndex(index),\n                                        className: \"flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 \".concat(index === currentImageIndex ? \"border-primary-500 ring-2 ring-primary-200\" : \"border-gray-200 hover:border-gray-300\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: image.url,\n                                            alt: image.alt || t(product.nameKey || product.name),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: product.nameKey ? t(product.nameKey) : product.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-lg leading-relaxed\",\n                                        children: product.descriptionKey ? t(product.descriptionKey) : product.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: t(\"productDetail.specifications\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: (product.specifications || product.specifications || []).map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: typeof spec === \"object\" && spec.key ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        t(spec.key),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 ml-2\",\n                                                                    children: spec.valueKey ? t(spec.valueKey) : spec.value\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: spec\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: t(\"productDetail.applications\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: (product.applications || product.applications || []).map((app, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-3 rounded-lg text-gray-700 text-sm font-medium\",\n                                                children: typeof app === \"object\" && app.key ? t(app.key) : app\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 9\n                            }, undefined),\n                            product.features && product.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: t(\"productDetail.features\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: product.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t(feature)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex-1 flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t(\"productDetail.getQuote\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border border-primary-600 text-primary-600 hover:bg-primary-50 px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex-1 flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Download_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t(\"productDetail.downloadSpec\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\components\\\\products\\\\ProductDetail.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductDetail, \"bfKWgPwqFVVY99Atx9RS5lcuw94=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage\n    ];\n});\n_c = ProductDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductDetail);\nvar _c;\n$RefreshReg$(_c, \"ProductDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/ProductDetail.tsx\n"));

/***/ })

});