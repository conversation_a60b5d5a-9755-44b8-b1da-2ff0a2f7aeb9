"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/bolts/page",{

/***/ "(app-pages-browser)/./src/app/products/bolts/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/products/bolts/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BoltsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_products_ProductDetail__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/products/ProductDetail */ \"(app-pages-browser)/./src/components/products/ProductDetail.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BoltsPage() {\n    _s();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const boltProducts = [\n        {\n            id: \"hex-bolt\",\n            slug: \"hex-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.hexBolt.name\",\n            descriptionKey: \"products.categories.bolts.hexBolt.description\",\n            shortDescKey: \"products.categories.bolts.hexBolt.description\",\n            specifications: [\n                {\n                    key: \"specifications.spec\",\n                    value: \"M6-M36\"\n                },\n                {\n                    key: \"specifications.strength\",\n                    value: \"4.8, 6.8, 8.8, 10.9, 12.9\"\n                },\n                {\n                    key: \"specifications.material\",\n                    value: \"Q235, 35K, 45K, 40Cr\"\n                },\n                {\n                    key: \"specifications.surface\",\n                    value: t(\"surfaceTreatments.zinc\") + \", \" + t(\"surfaceTreatments.blackening\") + \", \" + t(\"surfaceTreatments.phosphating\")\n                }\n            ],\n            applications: [\n                {\n                    key: \"mechanical\"\n                },\n                {\n                    key: \"construction\"\n                },\n                {\n                    key: \"automotive\"\n                },\n                {\n                    key: \"electrical\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"Hex Bolt\"\n                },\n                {\n                    url: \"https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n                    alt: \"Hex Bolt Detail\"\n                }\n            ],\n            features: []\n        },\n        {\n            id: \"flange-bolt\",\n            slug: \"flange-bolt\",\n            category: \"bolts\",\n            nameKey: \"products.categories.bolts.flangeBolt.name\",\n            descriptionKey: \"products.categories.bolts.flangeBolt.description\",\n            shortDescKey: \"products.categories.bolts.flangeBolt.description\",\n            specifications: [\n                {\n                    key: \"spec\",\n                    value: \"规格: M6-M20\"\n                },\n                {\n                    key: \"strength\",\n                    value: \"强度等级: 8.8, 10.9\"\n                },\n                {\n                    key: \"material\",\n                    value: \"材质: 35K, 45K, 40Cr\"\n                },\n                {\n                    key: \"surface\",\n                    value: \"表面处理: 镀锌、达克罗\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"automotive\"\n                },\n                {\n                    key: \"mechanical\"\n                },\n                {\n                    key: \"precision\"\n                },\n                {\n                    key: \"high-strength\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"Flange Bolt\"\n                }\n            ],\n            features: []\n        },\n        {\n            id: \"u-bolt\",\n            slug: \"u-bolt\",\n            category: \"bolts\",\n            nameKey: \"U型螺栓\",\n            descriptionKey: \"管道固定专用U型螺栓，抱箍连接，安装方便\",\n            shortDescKey: \"管道固定专用U型螺栓，抱箍连接，安装方便\",\n            specifications: [\n                {\n                    key: \"spec\",\n                    value: \"规格: M8-M30\"\n                },\n                {\n                    key: \"material\",\n                    value: \"材质: Q235, 304不锈钢\"\n                },\n                {\n                    key: \"surface\",\n                    value: \"表面处理: 镀锌、不锈钢本色\"\n                },\n                {\n                    key: \"shape\",\n                    value: \"形状: 标准U型、加长U型\"\n                }\n            ],\n            applications: [\n                {\n                    key: \"pipe-fixing\"\n                },\n                {\n                    key: \"cable-tray\"\n                },\n                {\n                    key: \"equipment\"\n                },\n                {\n                    key: \"structure\"\n                }\n            ],\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                    alt: \"U Bolt\"\n                }\n            ],\n            features: []\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-r from-primary-600 to-primary-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-white mb-6\",\n                            children: t(\"products.categories.bolts.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-primary-100 max-w-3xl mx-auto\",\n                            children: t(\"products.categories.bolts.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 lg:py-24 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-16\",\n                        children: boltProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_ProductDetail__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                product: product\n                            }, product.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\lxfastener\\\\src\\\\app\\\\products\\\\bolts\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(BoltsPage, \"ot2YhC7pP10gRrIouBKIa40vomw=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage\n    ];\n});\n_c = BoltsPage;\nvar _c;\n$RefreshReg$(_c, \"BoltsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/bolts/page.tsx\n"));

/***/ })

});