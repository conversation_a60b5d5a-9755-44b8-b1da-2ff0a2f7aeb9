'use client'

import { MapPin, Phone, Mail, MessageCircle, Clock, Globe } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'

const ContactInfo = () => {
  const { t } = useLanguage()

  const contactItems = [
    {
      icon: MapPin,
      titleKey: 'contact.info.address',
      content: t('footer.address'),
      color: 'bg-blue-500'
    },
    {
      icon: Phone,
      titleKey: 'contact.info.phone',
      content: '86-310-6620015',
      color: 'bg-green-500'
    },
    {
      icon: MessageCircle,
      titleKey: 'contact.info.mobile',
      content: '86-15373477521',
      color: 'bg-purple-500'
    },
    {
      icon: Mail,
      titleKey: 'contact.info.email',
      content: '<EMAIL>',
      color: 'bg-red-500'
    },
    {
      icon: Mail,
      titleKey: 'contact.info.salesEmail',
      content: '<EMAIL>',
      color: 'bg-orange-500'
    },
    {
      icon: Clock,
      titleKey: 'contact.info.workTime',
      content: t('contact.info.monday') + ' 8:00-17:30',
      color: 'bg-indigo-500'
    }
  ]

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-3xl font-bold text-gray-900 mb-6">
          {t('contact.info.title')}
        </h2>
        <p className="text-gray-600 leading-relaxed mb-8">
          {t('contact.info.description')}
        </p>
      </div>

      {/* Contact Items */}
      <div className="space-y-6">
        {contactItems.map((item, index) => {
          const IconComponent = item.icon
          return (
            <div
              key={index}
              className="flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <div className={`w-12 h-12 ${item.color} rounded-lg flex items-center justify-center flex-shrink-0 animate-pulse-big`}>
                <IconComponent className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">
                  {t(item.titleKey)}
                </h3>
                <p className="text-gray-600">
                  {item.content}
                </p>
              </div>
            </div>
          )
        })}
      </div>

      {/* Map Placeholder */}
      <div className="mt-8">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">
          {t('contact.info.location')}
        </h3>
        <div className="aspect-video bg-gray-200 rounded-xl overflow-hidden">
          <div className="w-full h-full flex items-center justify-center text-gray-500">
            <div className="text-center">
              <Globe className="h-12 w-12 mx-auto mb-4" />
              <p>{t('contact.info.mapLoading')}</p>
              <p className="text-sm mt-2">{t('footer.address')}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Business Hours */}
      <div className="bg-primary-50 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('contact.info.businessHours')}
        </h3>
        <div className="space-y-2 text-gray-600">
          <div className="flex justify-between">
            <span>{t('contact.info.monday')}</span>
            <span className="font-medium">8:00 - 17:30</span>
          </div>
          <div className="flex justify-between">
            <span>{t('contact.info.saturday')}</span>
            <span className="font-medium">8:00 - 12:00</span>
          </div>
          <div className="flex justify-between">
            <span>{t('contact.info.sunday')}</span>
            <span className="font-medium text-red-500">{t('contact.info.closed')}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContactInfo
